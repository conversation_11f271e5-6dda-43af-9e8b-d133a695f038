# HubSpot Async Refactoring

## Overview

This refactoring addresses the issue where HubSpot functions in `functions/hubspot/handler.py` were blocking other functions when they needed to return values. The solution implements a webhook-based callback system for operations that require return values.

## Problem

The original HubSpot functions were synchronous and blocking, which meant:
1. Functions that called HubSpot operations had to wait for completion
2. Return values (especially HubSpot contact IDs) were needed for subsequent operations
3. This created performance bottlenecks in the application

## Solution

### 1. Webhook Callback System

**New file: `app/utils/hubspot/webhooks.py`**
- `generate_callback_token()`: Creates unique tokens for callback identification
- `store_callback_expectation()`: Stores callback metadata in cache (10-minute timeout)
- `handle_hubspot_callback()`: Processes incoming callbacks from HubSpot functions
- Individual callback handlers for each operation type

### 2. Updated HubSpot Handler

**Modified: `functions/hubspot/handler.py`**
- Added `send_callback()` function to send results back to Django
- Modified `main()` function to handle callback URLs and tokens
- Maintains backward compatibility with synchronous calls

### 3. Enhanced Client Functions

**Modified: `app/utils/hubspot/client.py`**
- Added `CALLBACK_URL` configuration
- Updated `call_do_function()` to support callback tokens
- Added async versions of all functions:
  - `create_hubspot_contact_async()`
  - `update_hubspot_contact_async()`
  - `subscribe_user_to_list_async()`
  - `unsubscribe_user_from_list_async()`

### 4. New Dispatcher Endpoint

**Modified: `app/apps/dispatcher/views.py`**
- Added `hubspot_callback()` endpoint at `/api/dispatcher/hubspot-callback/`
- Handles incoming callbacks from HubSpot functions
- Uses `AllowAny` permission (authentication via callback token)

### 5. Updated User Functions

**Modified: `app/utils/hubspot/users.py`**
- Added `use_async` parameter to `create_contact()` and `subscribe_email()`
- Async operations store callback expectations and return immediately
- Callbacks handle updating user records with HubSpot IDs

## Usage Examples

### Synchronous (existing behavior)
```python
# Still works as before
response = create_contact(user)
hubspot_id = response.get('id')
```

### Asynchronous (new)
```python
# Returns immediately, updates user.hubspot_id via callback
response = create_contact(user, use_async=True)
# response = {"status": "async", "callback_token": "uuid-here"}
```

### Email Subscription (async)
```python
# Non-blocking email subscription
subscribe_email(email, mailing_list_id, use_async=True)
```

## Implementation Details

### Callback Flow
1. Django function calls HubSpot async function with callback token
2. Callback expectation stored in cache with metadata
3. HubSpot function executes and sends result to callback URL
4. Django webhook receives callback and processes result
5. User records updated based on callback data

### Cache Storage
- Callback expectations stored in Django cache
- 10-minute timeout (sufficient for HubSpot operations)
- One-time use tokens (deleted after processing)

### Error Handling
- Failed callbacks logged and sent to Sentry
- Graceful degradation if callback expectations expire
- Maintains existing error handling for synchronous operations

## Benefits

1. **Non-blocking Operations**: Functions no longer wait for HubSpot responses
2. **Better Performance**: Reduced response times for user-facing operations
3. **Backward Compatibility**: Existing synchronous calls still work
4. **Scalability**: Can handle multiple concurrent HubSpot operations
5. **Reliability**: Callback system with proper error handling

## Configuration Required

### Environment Variables
Ensure these are set in your environment:
- `BASE_BACKEND_URL`: Used to construct callback URL
- Existing HubSpot and Digital Ocean Functions variables

### URL Routing
The callback endpoint is automatically available at:
`/api/dispatcher/hubspot-callback/`

## Migration Strategy

### Phase 1: Deploy Infrastructure
- Deploy the new webhook system
- Keep existing synchronous calls working

### Phase 2: Gradual Migration
- Update high-traffic operations to use async (like newsletter subscriptions)
- Monitor callback success rates

### Phase 3: Full Migration
- Convert remaining operations to async where beneficial
- Keep synchronous for operations that need immediate results

## Functions That Benefit Most

1. **`create_contact()`**: Returns HubSpot ID needed for subsequent operations
2. **`subscribe_email()`**: High-volume operation for newsletter signups
3. **`update_contact()`**: Frequent user profile updates

## Functions That Can Stay Synchronous

1. **`get_contact_by_email()`**: Read operations that need immediate results
2. **`get_contact_subscription_preferences()`**: Data retrieval operations
3. Operations where return values aren't critical for subsequent logic

## Testing

To test the async functionality:

1. **Newsletter Subscription**: Use the updated newsletter endpoint
2. **User Registration**: Create users with `use_async=True`
3. **Monitor Logs**: Check for callback success messages
4. **Verify Data**: Ensure HubSpot IDs are properly updated

## Monitoring

Watch for these log messages:
- `hubspot.create_contact_async: email (callback: token)`
- `Updated user email with HubSpot ID: id`
- `Callback sent to URL: status_code`
- `HubSpot callback error: error_message`
