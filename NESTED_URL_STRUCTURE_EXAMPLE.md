# Nested URL Structure for Messages

## New URL Structure

The message endpoints now use nested URLs that include the thread ID in the path, making the API more RESTful and intuitive.

### URL Patterns

#### Chat Messages
- **Get Messages**: `GET /api/chat/{thread_id}/messages/`
- **Create Message**: `POST /api/chat/{thread_id}/messages/`
- **Get Message**: `GET /api/chat/{thread_id}/messages/{message_id}/`
- **Update Message**: `PUT /api/chat/{thread_id}/messages/{message_id}/`
- **Delete Message**: `DELETE /api/chat/{thread_id}/messages/{message_id}/`
- **Get Replies**: `GET /api/chat/{thread_id}/messages/{message_id}/replies/`

#### Discussion Messages
- **Get Messages**: `GET /api/discussions/{thread_id}/messages/`
- **Create Message**: `POST /api/discussions/{thread_id}/messages/`
- **Get Message**: `GET /api/discussions/{thread_id}/messages/{message_id}/`
- **Update Message**: `PUT /api/discussions/{thread_id}/messages/{message_id}/`
- **Delete Message**: `DELETE /api/discussions/{thread_id}/messages/{message_id}/`
- **Get Replies**: `GET /api/discussions/{thread_id}/messages/{message_id}/replies/`

## API Usage Examples

### 1. Get All Messages in a Thread

```http
GET /api/chat/550e8400-e29b-41d4-a716-446655440001/messages/
```

**Response:**
```json
[
  {
    "id": "msg-001",
    "author": "user-123",
    "author_details": {
      "id": "user-123",
      "email": "<EMAIL>",
      "first_name": "John",
      "last_name": "Doe",
      "nickname": "johndoe",
      "picture": "https://example.com/photos/tiny/john.jpg"
    },
    "parent_message": null,
    "body": "Hello everyone!",
    "replies": [
      {
        "id": "msg-002",
        "author": "user-456",
        "author_details": {
          "id": "user-456",
          "email": "<EMAIL>",
          "first_name": "Jane",
          "last_name": "Smith",
          "nickname": "janesmith",
          "picture": "https://example.com/photos/tiny/jane.jpg"
        },
        "parent_message": "msg-001",
        "body": "Hi John!",
        "replies": [],
        "reply_count": 0,
        "created": "2024-01-15T10:30:00Z"
      }
    ],
    "reply_count": 1,
    "created": "2024-01-15T10:00:00Z"
  }
]
```

### 2. Create a New Message

```http
POST /api/chat/550e8400-e29b-41d4-a716-446655440001/messages/
Content-Type: application/json

{
  "body": "This is a new message in the thread"
}
```

**Response:**
```json
{
  "id": "msg-003",
  "thread": "550e8400-e29b-41d4-a716-446655440001",
  "author": "user-789",
  "author_details": {
    "id": "user-789",
    "email": "<EMAIL>",
    "first_name": "Bob",
    "last_name": "Johnson",
    "nickname": "bobjohnson",
    "picture": "https://example.com/photos/tiny/bob.jpg"
  },
  "parent_message": null,
  "body": "This is a new message in the thread",
  "replies": [],
  "reply_count": 0,
  "created": "2024-01-15T11:00:00Z"
}
```

### 3. Create a Reply to a Message

```http
POST /api/chat/550e8400-e29b-41d4-a716-446655440001/messages/
Content-Type: application/json

{
  "parent_message": "msg-001",
  "body": "This is a reply to the first message"
}
```

**Response:**
```json
{
  "id": "msg-004",
  "thread": "550e8400-e29b-41d4-a716-446655440001",
  "author": "user-789",
  "author_details": {
    "id": "user-789",
    "email": "<EMAIL>",
    "first_name": "Bob",
    "last_name": "Johnson",
    "nickname": "bobjohnson",
    "picture": "https://example.com/photos/tiny/bob.jpg"
  },
  "parent_message": "msg-001",
  "body": "This is a reply to the first message",
  "replies": [],
  "reply_count": 0,
  "created": "2024-01-15T11:05:00Z"
}
```

### 4. Get Replies for a Specific Message

```http
GET /api/chat/550e8400-e29b-41d4-a716-446655440001/messages/msg-001/replies/
```

**Response:**
```json
[
  {
    "id": "msg-002",
    "thread": "550e8400-e29b-41d4-a716-446655440001",
    "author": "user-456",
    "author_details": {
      "id": "user-456",
      "email": "<EMAIL>",
      "first_name": "Jane",
      "last_name": "Smith",
      "nickname": "janesmith",
      "picture": "https://example.com/photos/tiny/jane.jpg"
    },
    "parent_message": "msg-001",
    "body": "Hi John!",
    "replies": [],
    "reply_count": 0,
    "created": "2024-01-15T10:30:00Z"
  },
  {
    "id": "msg-004",
    "thread": "550e8400-e29b-41d4-a716-446655440001",
    "author": "user-789",
    "author_details": {
      "id": "user-789",
      "email": "<EMAIL>",
      "first_name": "Bob",
      "last_name": "Johnson",
      "nickname": "bobjohnson",
      "picture": "https://example.com/photos/tiny/bob.jpg"
    },
    "parent_message": "msg-001",
    "body": "This is a reply to the first message",
    "replies": [],
    "reply_count": 0,
    "created": "2024-01-15T11:05:00Z"
  }
]
```

## Benefits of Nested URL Structure

1. **RESTful Design**: URLs clearly show the resource hierarchy
2. **Automatic Thread Association**: Thread ID is automatically extracted from URL
3. **Cleaner Payloads**: No need to include thread ID in request body
4. **Better Security**: Thread access is validated automatically
5. **Intuitive API**: URL structure matches the data relationship
6. **Consistent Pattern**: Same pattern for both chats and discussions

## Frontend Integration

```javascript
class MessageAPI {
  constructor(baseURL) {
    this.baseURL = baseURL;
  }

  // Get all messages in a chat thread
  async getChatMessages(threadId) {
    const response = await fetch(`${this.baseURL}/chat/${threadId}/messages/`);
    return response.json();
  }

  // Get all messages in a discussion thread
  async getDiscussionMessages(threadId) {
    const response = await fetch(`${this.baseURL}/discussions/${threadId}/messages/`);
    return response.json();
  }

  // Create a new chat message
  async createChatMessage(threadId, body) {
    const response = await fetch(`${this.baseURL}/chat/${threadId}/messages/`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ body })
    });
    return response.json();
  }

  // Create a new discussion message
  async createDiscussionMessage(threadId, body) {
    const response = await fetch(`${this.baseURL}/discussions/${threadId}/messages/`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ body })
    });
    return response.json();
  }

  // Create a reply (works for both chats and discussions)
  async createReply(threadId, parentMessageId, body, isDiscussion = false) {
    const endpoint = isDiscussion ? 'discussions' : 'chat';
    const response = await fetch(`${this.baseURL}/${endpoint}/${threadId}/messages/`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        parent_message: parentMessageId,
        body
      })
    });
    return response.json();
  }

  // Get replies for a message
  async getReplies(threadId, messageId, isDiscussion = false) {
    const endpoint = isDiscussion ? 'discussions' : 'chat';
    const response = await fetch(`${this.baseURL}/${endpoint}/${threadId}/messages/${messageId}/replies/`);
    return response.json();
  }
}

// Usage
const api = new MessageAPI('https://api.example.com');

// Get chat messages
const chatMessages = await api.getChatMessages('thread-uuid');

// Get discussion messages
const discussionMessages = await api.getDiscussionMessages('thread-uuid');

// Create a chat message
const newChatMessage = await api.createChatMessage('thread-uuid', 'Hello world!');

// Create a discussion message
const newDiscussionMessage = await api.createDiscussionMessage('thread-uuid', 'What do you think?');

// Create a reply in chat
const chatReply = await api.createReply('thread-uuid', 'msg-001', 'Great point!', false);

// Create a reply in discussion
const discussionReply = await api.createReply('thread-uuid', 'msg-001', 'I agree!', true);
```
