# Discourse Attachments API

## Overview

The discourse system now supports image file attachments for both Chat and Discussion messages. This implementation follows the same pattern as profile image uploads, using a separate S3 storage location and unique file naming.

## Key Features

- **Image Files Only**: Only image files are allowed for attachments
- **Separate Storage**: Uses `DiscoursePublicMediaStorage` with 'discourse' location
- **Unique Naming**: Files are named using format: `{thread_id}_{user_id}_{uuid8chars}.{extension}`
- **Two-Step Process**: Upload attachment first, then create message with attachment URL
- **Works for Both**: Chat and Discussion messages

## API Endpoints

### 1. Upload Attachment

Upload an image file and get the attachment URL to use when creating messages.

**Endpoint:**
```http
POST /api/chat/{thread_id}/upload_attachment/
POST /api/discussions/{thread_id}/upload_attachment/
```

**Request:**
- **Method**: POST
- **Content-Type**: multipart/form-data
- **Body**: Form data with 'file' field containing the image

**Example Request:**
```bash
curl -X POST \
  -H "Authorization: Bearer your_jwt_token" \
  -F "file=@image.jpg" \
  https://api.example.com/api/discussions/550e8400-e29b-41d4-a716-************/upload_attachment/
```

**Response (201 Created):**
```json
{
  "attachment_url": "https://bucket.region.digitaloceanspaces.com/discourse/550e8400-e29b-41d4-a716-************_123_a1b2c3d4.jpg",
  "message": "Attachment uploaded successfully"
}
```

**Error Responses:**
```json
// No file provided (400)
{
  "detail": "No file provided"
}

// Invalid file type (400)
{
  "detail": "Only image files are allowed"
}

// Upload failed (400)
{
  "detail": "Failed to upload attachment"
}
```

### 2. Create Message with Attachment

Use the attachment URL from step 1 when creating a message.

**Endpoint:**
```http
POST /api/chat/{thread_id}/messages/
POST /api/discussions/{thread_id}/messages/
```

**Request Body:**
```json
{
  "body": "Check out this image!",
  "attachment_url": "https://bucket.region.digitaloceanspaces.com/discourse/550e8400-e29b-41d4-a716-************_123_a1b2c3d4.jpg"
}
```

**Response:**
```json
{
  "id": "msg-001",
  "thread": "550e8400-e29b-41d4-a716-************",
  "author": "user-123",
  "author_details": {
    "id": "user-123",
    "email": "<EMAIL>",
    "first_name": "John",
    "last_name": "Doe",
    "nickname": "johndoe",
    "picture": "https://example.com/photos/tiny/john.jpg"
  },
  "parent_message": null,
  "body": "Check out this image!",
  "attachment": null,
  "attachment_url": "https://bucket.region.digitaloceanspaces.com/discourse/550e8400-e29b-41d4-a716-************_123_a1b2c3d4.jpg",
  "replies": [],
  "reply_count": 0,
  "created": "2024-01-15T10:00:00Z"
}
```

## WebSocket Integration

### Send Message with Attachment via WebSocket

After uploading an attachment, you can send the attachment URL via WebSocket:

**WebSocket Message:**
```json
{
  "type": "message",
  "body": "Check out this image!",
  "attachment_url": "https://bucket.region.digitaloceanspaces.com/discourse/550e8400-e29b-41d4-a716-************_123_a1b2c3d4.jpg"
}
```

**WebSocket Response:**
```json
{
  "type": "message",
  "message": {
    "id": "msg-001",
    "thread": "550e8400-e29b-41d4-a716-************",
    "author": "user-123",
    "author_details": {
      "id": "user-123",
      "email": "<EMAIL>",
      "first_name": "John",
      "last_name": "Doe",
      "nickname": "johndoe",
      "picture": "https://example.com/photos/tiny/john.jpg"
    },
    "parent_message": null,
    "body": "Check out this image!",
    "attachment": null,
    "attachment_url": "https://bucket.region.digitaloceanspaces.com/discourse/550e8400-e29b-41d4-a716-************_123_a1b2c3d4.jpg",
    "replies": [],
    "reply_count": 0,
    "created": "2024-01-15T10:00:00Z"
  }
}
```

## Frontend Implementation Example

### JavaScript Upload Function

```javascript
class DiscourseAttachments {
  constructor(baseURL, token) {
    this.baseURL = baseURL;
    this.token = token;
  }

  // Upload an image attachment
  async uploadAttachment(threadId, file, isDiscussion = false) {
    const endpoint = isDiscussion ? 'discussions' : 'chat';
    const formData = new FormData();
    formData.append('file', file);

    const response = await fetch(`${this.baseURL}/${endpoint}/${threadId}/upload_attachment/`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.token}`
      },
      body: formData
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail || 'Upload failed');
    }

    return response.json();
  }

  // Create message with attachment
  async createMessageWithAttachment(threadId, body, attachmentUrl, isDiscussion = false) {
    const endpoint = isDiscussion ? 'discussions' : 'chat';
    
    const response = await fetch(`${this.baseURL}/${endpoint}/${threadId}/messages/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.token}`
      },
      body: JSON.stringify({
        body,
        attachment_url: attachmentUrl
      })
    });

    return response.json();
  }

  // Complete flow: upload and send message
  async sendMessageWithImage(threadId, body, imageFile, isDiscussion = false) {
    try {
      // Step 1: Upload the image
      const uploadResult = await this.uploadAttachment(threadId, imageFile, isDiscussion);
      
      // Step 2: Create message with attachment URL
      const message = await this.createMessageWithAttachment(
        threadId, 
        body, 
        uploadResult.attachment_url, 
        isDiscussion
      );
      
      return message;
    } catch (error) {
      console.error('Failed to send message with image:', error);
      throw error;
    }
  }
}

// Usage example
const attachments = new DiscourseAttachments('https://api.example.com/api', 'your_jwt_token');

// Handle file input change
document.getElementById('file-input').addEventListener('change', async (event) => {
  const file = event.target.files[0];
  if (file && file.type.startsWith('image/')) {
    try {
      const message = await attachments.sendMessageWithImage(
        'thread-uuid',
        'Check out this image!',
        file,
        true // isDiscussion
      );
      console.log('Message sent:', message);
    } catch (error) {
      console.error('Error:', error.message);
    }
  } else {
    alert('Please select an image file');
  }
});
```

### React Component Example

```jsx
import React, { useState } from 'react';

const AttachmentUpload = ({ threadId, isDiscussion, onMessageSent }) => {
  const [uploading, setUploading] = useState(false);
  const [message, setMessage] = useState('');

  const handleFileUpload = async (event) => {
    const file = event.target.files[0];
    if (!file || !file.type.startsWith('image/')) {
      alert('Please select an image file');
      return;
    }

    setUploading(true);
    try {
      const attachments = new DiscourseAttachments('/api', localStorage.getItem('token'));
      const result = await attachments.sendMessageWithImage(threadId, message, file, isDiscussion);
      
      onMessageSent(result);
      setMessage('');
      event.target.value = ''; // Clear file input
    } catch (error) {
      alert(`Upload failed: ${error.message}`);
    } finally {
      setUploading(false);
    }
  };

  return (
    <div className="attachment-upload">
      <textarea
        value={message}
        onChange={(e) => setMessage(e.target.value)}
        placeholder="Type your message..."
        disabled={uploading}
      />
      <div className="upload-controls">
        <input
          type="file"
          accept="image/*"
          onChange={handleFileUpload}
          disabled={uploading}
        />
        {uploading && <span>Uploading...</span>}
      </div>
    </div>
  );
};

export default AttachmentUpload;
```

## Storage Configuration

The attachment system uses a dedicated S3 storage configuration:

```python
# config/storage.py
class DiscoursePublicMediaStorage(S3Storage):
    location = 'discourse'
    default_acl = 'public-read'
    file_overwrite = False
```

## File Naming Convention

Files are automatically renamed using this pattern:
```
{thread_id}_{user_id}_{uuid8chars}.{original_extension}
```

Example:
```
550e8400-e29b-41d4-a716-************_123_a1b2c3d4.jpg
```

This ensures:
- **Uniqueness**: UUID prevents naming conflicts
- **Traceability**: Thread and user IDs for debugging
- **Organization**: All discourse files in one location

## Security & Validation

- **Authentication Required**: All endpoints require valid JWT token
- **Thread Access**: Users must have access to the thread to upload attachments
- **File Type Validation**: Only image files are accepted
- **Content Type Check**: Server validates MIME type starts with 'image/'

## Error Handling

The system provides clear error messages for common issues:
- Missing file in request
- Invalid file type (non-image)
- Upload failures
- Thread access denied
- Authentication errors

## Backward Compatibility

The implementation maintains backward compatibility:
- Existing `attachment` field still works
- New `attachment_url` field takes precedence in serializers
- Old messages continue to work without changes
