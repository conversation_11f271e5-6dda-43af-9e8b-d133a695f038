# Frontend API Documentation

This document provides comprehensive API documentation for frontend developers consuming the Advaya API.

## Base URL
All endpoints are prefixed with `/api/`

## Authentication
Include JWT token in Authorization header: `Authorization: Bearer <token>`

Permission levels:
- **Anonymous**: No authentication required
- **Authenticated**: Valid JWT token required
- **Staff**: Staff user privileges required
- **Staff or Own Account**: Staff privileges or accessing own account

---

## Products - `/api/products`

**ViewSet**: `UserContentViewSet`

### GET `/api/products/`
**Description**: List user's content organized by content type
**Permissions**: Authenticated
**Query Parameters**: None
**Response**:
```json
{
  "courses": [
    {
      "slug": "mindfulness-basics",
      "content_type": "courses",
      "is_active": true,
      "bookmarked": false
    },
    {
      "slug": "advanced-meditation",
      "content_type": "courses",
      "is_active": true,
      "bookmarked": true
    }
  ],
  "clubs": [
    {
      "slug": "weekly-meditation-circle",
      "content_type": "events",
      "is_active": true,
      "bookmarked": false
    }
  ],
  "films": [
    {
      "slug": "wisdom-of-ages",
      "content_type": "films",
      "is_active": true,
      "bookmarked": true
    }
  ],
  "podcasts": [
    {
      "slug": "daily-dharma",
      "content_type": "podcasts",
      "is_active": true,
      "bookmarked": false
    }
  ],
  "articles": [
    {
      "slug": "introduction-to-mindfulness",
      "content_type": "articles",
      "is_active": true,
      "bookmarked": true
    }
  ]
}
```

### GET `/api/products/{id}/`
**Description**: Retrieve specific user content item with full product details
**Permissions**: Authenticated
**Path Parameters**:
- `id`: UserContent ID
**Response**:
```json
{
  "slug": "mindfulness-basics",
  "title": "Mindfulness Basics Course",
  "headline": "Learn the fundamentals of mindfulness meditation",
  "description": "A comprehensive introduction to mindfulness practices...",
  "picture": "https://cdn.advaya.co/courses/mindfulness-basics/cover.jpg",
  "thumbnail": "https://cdn.advaya.co/courses/mindfulness-basics/thumb.jpg",
  "teachers": [
    {
      "name": "Sarah Johnson",
      "slug": "sarah-johnson",
      "bio": "Mindfulness teacher with 15 years experience",
      "picture": "https://cdn.advaya.co/teachers/sarah-johnson.jpg"
    }
  ],
  "categories": [
    {
      "name": "Meditation",
      "slug": "meditation"
    }
  ],
  "duration": 3600,
  "sessions": [
    {
      "title": "Introduction to Mindfulness",
      "slug": "intro-mindfulness",
      "duration": 1800
    }
  ],
  "price": 49.99,
  "currency": "USD",
  "startDateTime": "2024-01-15T10:00:00Z",
  "endDateTime": "2024-01-15T11:00:00Z",
  "chargebee_entity_id": "course_mindfulness_basics",
  "hubspot_list_id": "course_subscribers_123"
}
```

### GET `/api/products/courses/`
**Description**: List user's courses
**Permissions**: Authenticated
**Response**:
```json
[
  {
    "id": "uc-123",
    "slug": "mindfulness-basics",
    "content_type": "courses",
    "is_active": true,
    "bookmarked": false,
    "created": "2024-01-01T10:00:00Z",
    "updated": "2024-01-01T10:00:00Z"
  }
]
```

### GET `/api/products/clubs/`
**Description**: List user's clubs (alias for events)
**Permissions**: Authenticated
**Response**:
```json
[
  {
    "id": "uc-456",
    "slug": "weekly-meditation-circle",
    "content_type": "events",
    "is_active": true,
    "bookmarked": true,
    "created": "2024-01-01T10:00:00Z",
    "updated": "2024-01-01T10:00:00Z"
  }
]
```

### GET `/api/products/events/`
**Description**: List user's events
**Permissions**: Authenticated
**Response**: Same format as clubs

### GET `/api/products/films/`
**Description**: List user's films
**Permissions**: Authenticated
**Response**:
```json
[
  {
    "id": "uc-789",
    "slug": "wisdom-of-ages",
    "content_type": "films",
    "is_active": true,
    "bookmarked": false,
    "created": "2024-01-01T10:00:00Z",
    "updated": "2024-01-01T10:00:00Z"
  }
]
```

### GET `/api/products/podcasts/`
**Description**: List user's podcasts
**Permissions**: Authenticated
**Response**: Same format as films

### GET `/api/products/articles/`
**Description**: List user's articles
**Permissions**: Authenticated
**Response**: Same format as films

### GET `/api/products/favourites/`
**Description**: List user's bookmarked content
**Permissions**: Authenticated
**Response**:
```json
[
  {
    "id": "uc-101",
    "slug": "introduction-to-mindfulness",
    "content_type": "articles",
    "is_active": true,
    "bookmarked": true,
    "created": "2024-01-01T10:00:00Z",
    "updated": "2024-01-01T10:00:00Z"
  }
]
```

### POST `/api/products/process_existing/`
**Description**: Process existing user content
**Permissions**: Authenticated
**Request Body**:
```json
{
  "content_type": "courses",
  "action": "sync",
  "force_update": false
}
```
**Response**:
```json
{
  "status": "success",
  "processed_count": 15,
  "message": "Content processing completed"
}
```

### GET `/api/products/member_suggestions/`
**Description**: Get content suggestions for user
**Permissions**: Authenticated
**Response**:
```json
{
  "upcoming_webinars": [
    {
      "contentType": "webinars",
      "slug": "webinar-slug",
      "directLink": "webinars/webinar-slug",
      "title": "Webinar Title",
      "headline": "Webinar Headline",
      "picture": "https://image-url.jpg",
      "thumbnail": "https://thumbnail-url.jpg",
      "teachers": [
        {
          "name": "Teacher Name",
          "slug": "teacher-slug",
          "picture": "https://teacher-image.jpg"
        }
      ],
      "categories": ["category-slug-1", "category-slug-2"],
      "startDateTime": "2024-01-01T10:00:00Z",
      "endDateTime": "2024-01-01T11:00:00Z",
      "date": "2024-01-01",
      "chargebee_entity_id": "webinar_123",
      "hubspot_list_id": "list_456",
      "lastUpdated": "2024-01-01T09:00:00Z"
    }
  ],
  "upcoming_clubs": [
    {
      "contentType": "clubs",
      "slug": "club-session-slug",
      "directLink": "clubs/club-session-slug",
      "title": "Club Session Title",
      "headline": "Session Headline",
      "picture": "https://image-url.jpg",
      "thumbnail": "https://thumbnail-url.jpg",
      "teachers": [...],
      "categories": [...],
      "startDateTime": "2024-01-02T18:00:00Z",
      "endDateTime": "2024-01-02T19:30:00Z",
      "date": "2024-01-02",
      "chargebee_entity_id": "club_789",
      "hubspot_list_id": "list_012",
      "lastUpdated": "2024-01-01T09:00:00Z"
    }
  ]
}
```

---

## Users - `/api/users`

**ViewSet**: `UserViewSet`

### POST `/api/users/`
**Description**: Create new user account
**Permissions**: Anonymous
**Request Body**:
```json
{
  "email": "<EMAIL>",
  "password": "SecurePassword123!",
  "first_name": "John",
  "last_name": "Doe",
  "terms_accepted": true,
  "newsletter_subscription": false
}
```
**Response**:
```json
{
  "id": "user-uuid-123",
  "email": "<EMAIL>",
  "first_name": "John",
  "last_name": "Doe",
  "is_verified": false,
  "is_active": true,
  "created": "2024-01-01T10:00:00Z",
  "profile": {
    "id": "profile-uuid-456",
    "public_details": {
      "first_name": "John",
      "last_name": "Doe",
      "nickname": "",
      "profile_photo": "",
      "country": ""
    },
    "interests": {},
    "created": "2024-01-01T10:00:00Z"
  }
}
```

### GET `/api/users/{id}/`
**Description**: Retrieve user details
**Permissions**: Staff or Own Account
**Path Parameters**:
- `id`: User ID or "current" for authenticated user
**Response**:
```json
{
  "id": "user-uuid-123",
  "email": "<EMAIL>",
  "first_name": "John",
  "last_name": "Doe",
  "is_verified": true,
  "is_active": true,
  "created": "2024-01-01T10:00:00Z",
  "updated": "2024-01-02T15:30:00Z",
  "profile": {
    "id": "profile-uuid-456",
    "public_details": {
      "first_name": "John",
      "last_name": "Doe",
      "nickname": "johndoe",
      "profile_photo": "https://cdn.advaya.co/profiles/john-doe.jpg",
      "country": "United Kingdom",
      "bio": "Meditation practitioner and mindfulness enthusiast"
    },
    "interests": {
      "meditation": true,
      "philosophy": true,
      "wellness": false
    },
    "created": "2024-01-01T10:00:00Z",
    "updated": "2024-01-02T15:30:00Z"
  }
}
```

### GET `/api/users/current/`
**Description**: Get current authenticated user with detailed content data
**Permissions**: Authenticated
**Response**:
```json
{
  "id": "user-uuid",
  "email": "<EMAIL>",
  "first_name": "John",
  "last_name": "Doe",
  "profile": {...},
  "courses": {
    "course-slug-1": {
      "contentType": "courses",
      "slug": "course-slug-1",
      "directLink": "courses/course-slug-1",
      "title": "Course Title",
      "headline": "Course Headline",
      "picture": "https://image-url.jpg",
      "thumbnail": "https://thumbnail-url.jpg",
      "teachers": [
        {
          "name": "Teacher Name",
          "slug": "teacher-slug",
          "picture": "https://teacher-image.jpg"
        }
      ],
      "categories": ["category-slug-1"],
      "startDateTime": "2024-01-01T10:00:00Z",
      "endDateTime": "2024-01-01T11:00:00Z",
      "date": "2024-01-01",
      "chargebee_entity_id": "course_123",
      "hubspot_list_id": "list_456",
      "lastUpdated": "2024-01-01T09:00:00Z",
      "watchedChapters": ["session-1", "session-2"],
      "isRegistered": true,
      "isPurchased": true,
      "isActive": true,
      "isFavourite": false
    }
  },
  "clubs": {
    "club-slug-1": {
      "contentType": "clubs",
      "slug": "club-slug-1",
      "directLink": "clubs/club-slug-1",
      "title": "Club Title",
      "headline": "Club Headline",
      "picture": "https://image-url.jpg",
      "thumbnail": "https://thumbnail-url.jpg",
      "teachers": [...],
      "categories": [...],
      "startDateTime": "2024-01-02T18:00:00Z",
      "endDateTime": "2024-01-02T19:30:00Z",
      "date": "2024-01-02",
      "chargebee_entity_id": "club_789",
      "hubspot_list_id": "list_012",
      "lastUpdated": "2024-01-01T09:00:00Z",
      "watchedChapters": [],
      "isRegistered": true,
      "isPurchased": false,
      "isActive": true,
      "isFavourite": true
    }
  },
  "films": {...},
  "podcasts": {...},
  "articles": {...}
}
```

### PUT/PATCH `/api/users/{id}/`
**Description**: Update user details
**Permissions**: Staff or Own Account
**Request Body**:
```json
{
  "first_name": "Jonathan",
  "last_name": "Doe",
  "profile": {
    "public_details": {
      "nickname": "jon_doe",
      "country": "United States",
      "bio": "Updated bio with new interests"
    },
    "interests": {
      "meditation": true,
      "philosophy": true,
      "wellness": true,
      "yoga": true
    }
  }
}
```
**Response**:
```json
{
  "id": "user-uuid-123",
  "email": "<EMAIL>",
  "first_name": "Jonathan",
  "last_name": "Doe",
  "is_verified": true,
  "is_active": true,
  "created": "2024-01-01T10:00:00Z",
  "updated": "2024-01-03T12:00:00Z",
  "profile": {
    "id": "profile-uuid-456",
    "public_details": {
      "first_name": "Jonathan",
      "last_name": "Doe",
      "nickname": "jon_doe",
      "profile_photo": "https://cdn.advaya.co/profiles/john-doe.jpg",
      "country": "United States",
      "bio": "Updated bio with new interests"
    },
    "interests": {
      "meditation": true,
      "philosophy": true,
      "wellness": true,
      "yoga": true
    },
    "created": "2024-01-01T10:00:00Z",
    "updated": "2024-01-03T12:00:00Z"
  }
}
```

### DELETE `/api/users/current/`
**Description**: Deactivate user account
**Permissions**: Staff or Own Account
**Response**:
```json
{}
```

### GET `/api/users/verify/`
**Description**: Verify user email
**Permissions**: Authenticated
**Query Parameters**:
- `token`: Verification token
**Response**:
```json
{
  "id": "user-uuid-123",
  "email": "<EMAIL>",
  "first_name": "John",
  "last_name": "Doe",
  "is_verified": true,
  "is_active": true,
  "created": "2024-01-01T10:00:00Z",
  "updated": "2024-01-01T11:00:00Z"
}
```

### GET `/api/users/activate/`
**Description**: Activate user account
**Permissions**: Authenticated
**Query Parameters**:
- `token`: Activation token
**Response**: Same format as verify endpoint

### POST `/api/users/newsletter_subscription/`
**Description**: Subscribe to newsletter
**Permissions**: Anonymous
**Request Body**:
```json
{
  "email": "<EMAIL>"
}
```
**Response**:
```json
{
  "status": "success",
  "message": "Successfully subscribed to newsletter"
}
```

### GET `/api/users/request_password_reset/`
**Description**: Request password reset
**Permissions**: Anonymous
**Query Parameters**:
- `email`: User email address
**Response**:
```json
{
  "status": "success",
  "code": 200,
  "message": "Check your inbox for the password reset link.",
  "data": {
    "detail": "Check your inbox for the password reset link."
  }
}
```

### POST `/api/users/set_user_plan/`
**Description**: Set user subscription plan (Chargebee webhook)
**Permissions**: Chargebee Request
**Request Body**:
```json
{
  "user_id": "user-uuid-123",
  "plan_id": "premium_monthly",
  "subscription_id": "cb_sub_123",
  "status": "active",
  "trial_end": "2024-02-01T00:00:00Z"
}
```
**Response**:
```json
{
  "status": "success",
  "message": "User plan updated successfully"
}
```

---

## Members - `/api/members`

**ViewSet**: `MemberViewSet`

### GET `/api/members/`
**Description**: List community members with search and filtering
**Permissions**: Authenticated
**Query Parameters**:
- `limit`: Number of results (default: 20)
- `offset`: Pagination offset (default: 0)
- `query`: Search in first_name, last_name, nickname
- `country`: Filter by country
**Response**:
```json
{
  "results": [
    {
      "id": "user-id",
      "public_details": {
        "first_name": "John",
        "last_name": "Doe",
        "nickname": "johndoe",
        "profile_photo": "photo-url",
        "country": "UK"
      },
      "interests": {...}
    }
  ],
  "total": 150,
  "limit": 20,
  "offset": 0
}
```

### GET `/api/members/{id}/`
**Description**: Retrieve member profile
**Permissions**: Authenticated
**Path Parameters**:
- `id`: User ID
**Response**: PublicProfileSerializer data

### GET `/api/members/{id}/follow/`
**Description**: Follow a member
**Permissions**: Authenticated
**Path Parameters**:
- `id`: User ID to follow
**Response**: Updated profile data

### GET `/api/members/{id}/unfollow/`
**Description**: Unfollow a member
**Permissions**: Authenticated
**Path Parameters**:
- `id`: User ID to unfollow
**Response**: Updated profile data

---

## Data - `/api/data`

**ViewSet**: `UserDataViewSet`

### POST `/api/data/collect_mux_video_view_data/`
**Description**: Collect video viewing data from Mux (Digital Ocean Functions)
**Permissions**: Digital Ocean Functions Request
**Request Body**:
```json
{
  "days": 35
}
```
**Response**:
```json
{}
```

---

## Chat Threads - `/api/chat/threads`

**ViewSet**: `ChatThreadViewSet` (discourse_type='chats')

### GET `/api/chat/threads/`
**Description**: List user's chat threads with caching
**Permissions**: Authenticated
**Query Parameters**:
- `exclude_public`: Boolean to exclude public threads
**Response**:
```json
[
  {
    "id": "thread-uuid",
    "title": "Chat Title",
    "discourse_type": "chats",
    "is_private": true,
    "participants": [...],
    "messages_count": 15,
    "last_message": {...},
    "created": "2024-01-01T00:00:00Z",
    "updated": "2024-01-01T00:00:00Z"
  }
]
```

### POST `/api/chat/threads/`
**Description**: Create new chat thread
**Permissions**: Authenticated
**Request Body**:
```json
{
  "title": "Project Discussion",
  "is_private": true,
  "participant_ids": ["user-uuid-456", "user-uuid-789"]
}
```
**Response**:
```json
{
  "id": "thread-uuid-123",
  "title": "Project Discussion",
  "discourse_type": "chats",
  "is_private": true,
  "participants": [
    {
      "id": "user-uuid-123",
      "first_name": "John",
      "last_name": "Doe",
      "profile": {
        "public_details": {
          "profile_photo": "https://cdn.advaya.co/profiles/john-doe.jpg",
          "nickname": "johndoe"
        }
      }
    },
    {
      "id": "user-uuid-456",
      "first_name": "Jane",
      "last_name": "Smith",
      "profile": {
        "public_details": {
          "profile_photo": "https://cdn.advaya.co/profiles/jane-smith.jpg",
          "nickname": "janesmith"
        }
      }
    }
  ],
  "messages_count": 0,
  "last_message": null,
  "created": "2024-01-01T10:00:00Z",
  "updated": "2024-01-01T10:00:00Z"
}
```

### GET `/api/chat/threads/{id}/`
**Description**: Retrieve specific chat thread
**Permissions**: Authenticated (must be participant)
**Response**: Same format as POST response above

### PUT/PATCH `/api/chat/threads/{id}/`
**Description**: Update chat thread
**Permissions**: Authenticated (must be participant)
**Request Body**:
```json
{
  "title": "Updated Project Discussion",
  "is_private": false
}
```
**Response**: Updated thread object (same format as GET response)

### DELETE `/api/chat/threads/{id}/`
**Description**: Delete chat thread
**Permissions**: Authenticated (must be participant)
**Response**:
```json
{}
```

### POST `/api/chat/threads/{id}/add_participant/`
**Description**: Add participant to thread
**Permissions**: Authenticated (must be participant)
**Request Body**:
```json
{
  "user_id": "user-uuid-999"
}
```
**Response**:
```json
{
  "status": "success",
  "message": "Participant added successfully",
  "participant": {
    "id": "user-uuid-999",
    "first_name": "Alice",
    "last_name": "Johnson",
    "profile": {
      "public_details": {
        "profile_photo": "https://cdn.advaya.co/profiles/alice-johnson.jpg",
        "nickname": "alicejohnson"
      }
    }
  }
}
```

### POST `/api/chat/threads/{id}/remove_participant/`
**Description**: Remove participant from thread
**Permissions**: Authenticated (must be participant)
**Request Body**:
```json
{
  "user_id": "user-uuid-999"
}
```
**Response**:
```json
{
  "status": "success",
  "message": "Participant removed successfully"
}
```

### POST `/api/chat/threads/{id}/upload_attachment/`
**Description**: Upload file attachment for thread
**Permissions**: Authenticated (must be participant)
**Request Body**: Multipart form with file field named "file"
**Response**:
```json
{
  "attachment_url": "https://s3.amazonaws.com/advaya-discourse/thread-uuid-123_user-uuid-456_image.jpg"
}
```

---

## Discussion Threads - `/api/discussions/threads`

**ViewSet**: `ChatThreadViewSet` (discourse_type='discussions')

Same endpoints as chat threads but for discussions. Key differences:
- Public discussions are accessible to all authenticated users
- Discussions require mandatory titles
- Thread list shows participated threads first, then public discussions

---

## Chat Messages - `/api/chat/{thread_id}/messages`

**ViewSet**: `ChatMessageViewSet`

### GET `/api/chat/{thread_id}/messages/`
**Description**: List messages in thread with caching and pagination
**Permissions**: Authenticated (must be thread participant)
**Path Parameters**:
- `thread_id`: Thread UUID
**Query Parameters**:
- `limit`: Number of messages (default: 30)
- `offset`: Pagination offset (default: 0)
**Response**:
```json
{
  "results": [
    {
      "id": "message-uuid",
      "body": "Message text",
      "author": {...},
      "attachment_url": "optional-file-url",
      "parent_message": null,
      "replies": [
        {
          "id": "reply-uuid",
          "body": "Reply text",
          "author": {...},
          "created": "2024-01-01T00:00:00Z"
        }
      ],
      "created": "2024-01-01T00:00:00Z"
    }
  ],
  "total": 100,
  "limit": 30,
  "offset": 0
}
```

### POST `/api/chat/{thread_id}/messages/`
**Description**: Create new message in thread
**Permissions**: Authenticated (must be thread participant)
**Request Body**:
```json
{
  "body": "Hello everyone! How is the project going?",
  "parent_message": null,
  "attachment_url": "https://s3.amazonaws.com/advaya-discourse/thread-uuid-123_user-uuid-456_document.pdf"
}
```
**Response**:
```json
{
  "id": "message-uuid-123",
  "body": "Hello everyone! How is the project going?",
  "author": {
    "id": "user-uuid-123",
    "first_name": "John",
    "last_name": "Doe",
    "profile": {
      "public_details": {
        "profile_photo": "https://cdn.advaya.co/profiles/john-doe.jpg",
        "nickname": "johndoe"
      }
    }
  },
  "attachment_url": "https://s3.amazonaws.com/advaya-discourse/thread-uuid-123_user-uuid-456_document.pdf",
  "parent_message": null,
  "replies": [],
  "created": "2024-01-01T10:30:00Z",
  "updated": "2024-01-01T10:30:00Z"
}
```

### GET `/api/chat/{thread_id}/messages/{id}/`
**Description**: Retrieve specific message
**Permissions**: Authenticated (must be thread participant)
**Response**: Same format as POST message response

### PUT/PATCH `/api/chat/{thread_id}/messages/{id}/`
**Description**: Update message
**Permissions**: Authenticated (must be message author)
**Request Body**:
```json
{
  "body": "Updated message text"
}
```
**Response**: Updated message object (same format as GET response)

### DELETE `/api/chat/{thread_id}/messages/{id}/`
**Description**: Delete message
**Permissions**: Authenticated (must be message author)
**Response**:
```json
{}
```

---

## Discussion Messages - `/api/discussions/{thread_id}/messages`

Same endpoints as chat messages but for discussion threads.

---

## Pages - `/api/pages`

**ViewSet**: `PagesViewSet`

### GET `/api/pages/`
**Description**: List available static pages
**Permissions**: Anonymous
**Response**: Array of page slugs
```json
[
  "about",
  "contact",
  "faq-and-contact-us",
  "homepage",
  "learn-landing",
  "privacy-policy",
  "terms-and-conditions"
]
```

### GET `/api/pages/{slug}/`
**Description**: Retrieve page content with caching
**Permissions**: Anonymous
**Path Parameters**:
- `slug`: Page slug
**Response**:
```json
{
  "slug": "about",
  "title": "About Advaya",
  "content": "<p>Advaya is a platform for wisdom and learning...</p>",
  "meta_description": "Learn about Advaya's mission and vision",
  "meta_keywords": ["about", "advaya", "wisdom", "learning"],
  "featured_image": "https://cdn.advaya.co/pages/about/hero.jpg",
  "published": true,
  "created": "2024-01-01T00:00:00Z",
  "updated": "2024-01-15T10:00:00Z"
}
```

---

## Categories - `/api/categories`

**ViewSet**: `CategoriesViewSet`

### GET `/api/categories/`
**Description**: List all content categories with caching
**Permissions**: Anonymous
**Response**:
```json
[
  {
    "slug": "meditation",
    "name": "Meditation",
    "description": "Practices and teachings on meditation",
    "color": "#4A90E2",
    "icon": "meditation-icon.svg",
    "content_count": 45,
    "featured": true
  },
  {
    "slug": "philosophy",
    "name": "Philosophy",
    "description": "Philosophical teachings and discussions",
    "color": "#7B68EE",
    "icon": "philosophy-icon.svg",
    "content_count": 32,
    "featured": false
  }
]
```

### GET `/api/categories/{slug}/`
**Description**: Retrieve specific category with caching
**Permissions**: Anonymous
**Path Parameters**:
- `slug`: Category slug
**Response**:
```json
{
  "slug": "meditation",
  "name": "Meditation",
  "description": "Practices and teachings on meditation",
  "long_description": "<p>Meditation is a practice where an individual uses a technique...</p>",
  "color": "#4A90E2",
  "icon": "meditation-icon.svg",
  "featured_image": "https://cdn.advaya.co/categories/meditation/hero.jpg",
  "content_count": 45,
  "featured": true,
  "related_categories": ["mindfulness", "wellness"],
  "created": "2024-01-01T00:00:00Z",
  "updated": "2024-01-15T10:00:00Z"
}
```

---

## Realms - `/api/realms`

**ViewSet**: `RealmsViewSet`

### GET `/api/realms/`
**Description**: List all realms with caching
**Permissions**: Anonymous
**Response**: Array of realm objects from search index

### GET `/api/realms/{slug}/`
**Description**: Retrieve specific realm with caching
**Permissions**: Anonymous
**Path Parameters**:
- `slug`: Realm slug
**Response**: Realm object from search index

---

## Entities - `/api/entities`

**ViewSet**: `EntitiesViewSet`

### GET `/api/entities/`
**Description**: List entities (teachers) with search and caching
**Permissions**: Anonymous
**Query Parameters**: Search parameters supported by Meilisearch
**Response**: ContentResultsSerializer format
```json
{
  "results": [...],
  "total": 50,
  "limit": 20,
  "offset": 0,
  "query": "search-term"
}
```

### GET `/api/entities/{slug}/`
**Description**: Retrieve specific entity with caching
**Permissions**: Anonymous
**Path Parameters**:
- `slug`: Entity slug
**Response**: Entity object from search index

### POST `/api/entities/{slug}/favourite/`
**Description**: Bookmark an entity
**Permissions**: Authenticated
**Response**: UserContent object

---

## Articles - `/api/articles`

**ViewSet**: `ArticlesViewSet`

### GET `/api/articles/`
**Description**: Search articles with caching
**Permissions**: Anonymous
**Query Parameters**: Search parameters supported by Meilisearch
**Response**: ContentResultsSerializer format

### GET `/api/articles/{slug}/`
**Description**: Retrieve specific article
**Permissions**: Anonymous (limited content without purchase)
**Path Parameters**:
- `slug`: Article slug
**Response**: Article object (cleaned for SEO if not purchased)

### GET `/api/articles/{slug}/purchase/`
**Description**: Generate purchase page for article
**Permissions**: Authenticated
**Response**:
```json
{
  "hosted_page": {
    "id": "cb_hosted_page_123",
    "url": "https://advaya.chargebee.com/hosted_pages/cb_hosted_page_123",
    "state": "created",
    "type": "checkout_one_time",
    "embed": true
  },
  "content": {
    "slug": "introduction-to-mindfulness",
    "title": "Introduction to Mindfulness",
    "price": 9.99,
    "currency": "USD"
  }
}
```

### POST `/api/articles/{slug}/favourite/`
**Description**: Bookmark an article
**Permissions**: Authenticated
**Response**:
```json
{
  "id": "uc-789",
  "slug": "introduction-to-mindfulness",
  "content_type": "articles",
  "is_active": false,
  "bookmarked": true,
  "on_demand": false,
  "created": "2024-01-01T10:00:00Z",
  "updated": "2024-01-01T10:00:00Z",
  "user": "user-uuid-123"
}
```

---

## Podcasts - `/api/podcasts`

**ViewSet**: `PodcastsViewSet`

### GET `/api/podcasts/`
**Description**: Search podcasts with caching
**Permissions**: Anonymous
**Query Parameters**: Search parameters supported by Meilisearch
**Response**: ContentResultsSerializer format

### GET `/api/podcasts/{slug}/`
**Description**: Retrieve specific podcast with caching
**Permissions**: Anonymous (limited content without purchase)
**Path Parameters**:
- `slug`: Podcast slug
**Response**: Podcast object (cleaned for SEO if not purchased)

### GET `/api/podcasts/{slug}/purchase/`
**Description**: Generate purchase page for podcast
**Permissions**: Authenticated
**Response**: Chargebee hosted page details

### POST `/api/podcasts/{slug}/favourite/`
**Description**: Bookmark a podcast
**Permissions**: Authenticated
**Response**: UserContent object

---

## Films - `/api/films`

**ViewSet**: `FilmsViewSet`

### GET `/api/films/`
**Description**: Search films with caching
**Permissions**: Anonymous
**Query Parameters**: Search parameters supported by Meilisearch
**Response**: ContentResultsSerializer format

### GET `/api/films/{slug}/`
**Description**: Retrieve specific film with caching
**Permissions**: Anonymous (limited content without purchase)
**Path Parameters**:
- `slug`: Film slug
**Response**: Film object (cleaned for SEO if not purchased)

### GET `/api/films/{slug}/purchase/`
**Description**: Generate purchase page for film
**Permissions**: Authenticated
**Response**: Chargebee hosted page details

### POST `/api/films/{slug}/favourite/`
**Description**: Bookmark a film
**Permissions**: Authenticated
**Response**: UserContent object

---

## Courses - `/api/courses`

**ViewSet**: `CoursesViewSet`

### GET `/api/courses/`
**Description**: Search courses with caching
**Permissions**: Anonymous
**Query Parameters**: Search parameters supported by Meilisearch
**Response**: ContentResultsSerializer format

### GET `/api/courses/{slug}/`
**Description**: Retrieve specific course with caching
**Permissions**: Anonymous (limited content without enrollment)
**Path Parameters**:
- `slug`: Course slug
**Response**: Course object (cleaned for SEO if not enrolled)

### GET `/api/courses/{slug}/purchase/`
**Description**: Generate purchase page for course
**Permissions**: Authenticated
**Response**: Chargebee hosted page details

### POST `/api/courses/{slug}/favourite/`
**Description**: Bookmark a course
**Permissions**: Authenticated
**Response**: UserContent object

### POST `/api/courses/{slug}/enrol/`
**Description**: Enroll user in course
**Permissions**: Authenticated
**Request Body**:
```json
{
  "enrollment_type": "free_trial"
}
```
**Response**:
```json
{
  "id": "enrollment-uuid-123",
  "user": "user-uuid-123",
  "course_slug": "mindfulness-basics",
  "enrollment_type": "free_trial",
  "status": "active",
  "progress": 0,
  "watched_sessions": [],
  "enrolled_at": "2024-01-01T10:00:00Z",
  "expires_at": "2024-02-01T10:00:00Z"
}
```

### GET `/api/courses/{slug}/sessions/`
**Description**: List course sessions with caching
**Permissions**: Authenticated (must be enrolled)
**Response**: ContentResultsSerializer format with sessions

### GET `/api/courses/{slug}/progress/`
**Description**: Get user's course progress
**Permissions**: Authenticated (must be enrolled)
**Response**: Progress data

### POST `/api/courses/{slug}/progress/`
**Description**: Update course progress
**Permissions**: Authenticated (must be enrolled)
**Request Body**:
```json
{
  "session_slug": "intro-mindfulness",
  "progress": 85,
  "completed": false,
  "watch_time": 1530
}
```
**Response**:
```json
{
  "status": "success",
  "message": "Progress updated successfully",
  "enrollment": {
    "id": "enrollment-uuid-123",
    "progress": 25,
    "watched_sessions": ["intro-mindfulness"],
    "total_sessions": 8,
    "completed_sessions": 1
  }
}
```

---

## Q&A - `/api/q-and-a`

**ViewSet**: `QuestionsAndAnswersViewSet`

### GET `/api/q-and-a/`
**Description**: Search Q&A content with caching
**Permissions**: Anonymous
**Query Parameters**: Search parameters supported by Meilisearch
**Response**: ContentResultsSerializer format

---

## Events - `/api/events`

**ViewSet**: `EventsViewSet`

### GET `/api/events/`
**Description**: Search events with caching
**Permissions**: Anonymous
**Query Parameters**: Search parameters supported by Meilisearch
**Response**: ContentResultsSerializer format

### GET `/api/events/{slug}/`
**Description**: Retrieve specific event with caching
**Permissions**: Anonymous (limited content without registration)
**Path Parameters**:
- `slug`: Event slug
**Response**: Event object (cleaned for SEO if not registered)

### GET `/api/events/{slug}/purchase/`
**Description**: Generate purchase page for event
**Permissions**: Authenticated
**Response**: Chargebee hosted page details

### POST `/api/events/{slug}/favourite/`
**Description**: Bookmark an event
**Permissions**: Authenticated
**Response**: UserContent object

### POST `/api/events/{slug}/register/`
**Description**: Register for event
**Permissions**: Authenticated
**Response**: Registration details

### GET `/api/events/series_sessions/`
**Description**: List event series sessions with caching
**Permissions**: Anonymous
**Response**: ContentResultsSerializer format with EventSessionPublicSerializer data

---

## Practice - `/api/practice`

**ViewSet**: `EventsViewSet` (same as events with different basename)

Same endpoints as `/api/events` but filtered for practice content.

---

## Clubs - `/api/clubs`

**ViewSet**: `EventsViewSet` (same as events with different basename)

Same endpoints as `/api/events` but filtered for club content.

---

## Audio Journeys - `/api/audio-journeys`

**ViewSet**: `EventsViewSet` (same as events with different basename)

Same endpoints as `/api/events` but filtered for audio journey content.

---

## Webinars - `/api/webinars`

**ViewSet**: `WebinarsViewSet`

### GET `/api/webinars/`
**Description**: Search webinars with caching
**Permissions**: Anonymous
**Query Parameters**: Search parameters supported by Meilisearch
**Response**: ContentResultsSerializer format

### GET `/api/webinars/{slug}/`
**Description**: Retrieve specific webinar with caching
**Permissions**: Anonymous (limited content without registration)
**Path Parameters**:
- `slug`: Webinar slug
**Response**: Webinar object (cleaned for SEO if not registered)

### GET `/api/webinars/{slug}/purchase/`
**Description**: Generate purchase page for webinar
**Permissions**: Authenticated
**Response**: Chargebee hosted page details

### POST `/api/webinars/{slug}/favourite/`
**Description**: Bookmark a webinar
**Permissions**: Authenticated
**Response**: UserContent object

### POST `/api/webinars/{slug}/register/`
**Description**: Register for webinar
**Permissions**: Authenticated
**Response**: Registration details

### GET `/api/webinars/{slug}/join/`
**Description**: Join webinar meeting
**Permissions**: Authenticated (must be registered)
**Response**: Zoom meeting join details

---

## Plans - `/api/plans`

**ViewSet**: `PlansViewSet`

### GET `/api/plans/`
**Description**: List subscription plans
**Permissions**: Anonymous
**Response**: Array of plan objects from search index

### GET `/api/plans/{slug}/`
**Description**: Retrieve specific plan
**Permissions**: Anonymous
**Path Parameters**:
- `slug`: Plan slug
**Response**: Plan object from search index

---

## Search - `/api/search`

**ViewSet**: `SearchViewSet`

### GET `/api/search/`
**Description**: Multi-index search across all content types
**Permissions**: Anonymous
**Query Parameters**: Search parameters supported by Meilisearch
**Response**:
```json
{
  "articles": {
    "results": [...],
    "total": 10,
    "limit": 20,
    "offset": 0,
    "query": "search-term"
  },
  "courses": {...},
  "events": {...},
  "films": {...},
  "podcasts": {...},
  "categories": {...},
  "teachers": {...}
}
```

---

## WebSocket Endpoints

### Chat WebSocket - `/ws/chat/{thread_id}/`

**Consumer**: `ChatConsumer`

**Connection**: Requires authentication and thread participation

**Incoming Messages**:
```json
{
  "type": "message",
  "body": "Message text",
  "parent_message": "optional-parent-uuid",
  "attachment_url": "optional-file-url"
}
```

**Outgoing Messages**:
```json
{
  "type": "message",
  "message": {
    "id": "message-uuid",
    "body": "Message text",
    "author": {...},
    "created": "2024-01-01T00:00:00Z"
  }
}
```

**History on Connect**:
```json
{
  "type": "history",
  "messages": [...]
}
```

### Chat List WebSocket - `/ws/chat/`

**Consumer**: `ChatListConsumer`

**Connection**: Requires authentication

**Outgoing Messages**:
```json
{
  "type": "thread_list",
  "threads": [...]
}
```

```json
{
  "type": "thread_update",
  "thread": {...}
}
```

### Discussion List WebSocket - `/ws/discussions/`

**Consumer**: `ChatListConsumer` (filtered for discussions)

Same format as chat list but for discussion threads.

---

## Data Structures

### Product Data Structure

Many endpoints return product data in this standardized format:

```json
{
  "contentType": "courses|events|films|podcasts|articles|webinars",
  "slug": "content-slug",
  "directLink": "content-type/content-slug",
  "title": "Content Title",
  "headline": "Content Headline",
  "picture": "https://image-url.jpg",
  "thumbnail": "https://thumbnail-url.jpg",
  "teachers": [
    {
      "name": "Teacher Name",
      "slug": "teacher-slug",
      "picture": "https://teacher-image.jpg"
    }
  ],
  "categories": ["category-slug-1", "category-slug-2"],
  "startDateTime": "2024-01-01T10:00:00Z",
  "endDateTime": "2024-01-01T11:00:00Z",
  "date": "2024-01-01",
  "chargebee_entity_id": "entity_123",
  "hubspot_list_id": "list_456",
  "lastUpdated": "2024-01-01T09:00:00Z"
}
```

### User Content Data (when user-specific data is included)

When endpoints include user-specific content data, the above structure is extended with:

```json
{
  ...productData,
  "watchedChapters": ["session-1", "session-2"],
  "isRegistered": true,
  "isPurchased": true,
  "isActive": true,
  "isFavourite": false
}
```

**Field Descriptions:**
- `watchedChapters`: Array of session slugs the user has watched
- `isRegistered`: Whether user is enrolled/registered for this content
- `isPurchased`: Whether user has purchased this content
- `isActive`: Whether the user's access to this content is active
- `isFavourite`: Whether user has bookmarked this content

**Endpoints that include User Content Data:**
- `GET /api/users/current/` - All user's content with full user data
- `GET /api/products/member_suggestions/` - Upcoming content recommendations
- Any endpoint that returns user's personal content collections

### Search Results Structure

Search and list endpoints return data in this paginated format:

```json
{
  "results": [...],
  "total": 100,
  "limit": 20,
  "offset": 0,
  "query": "search-term"
}
```

---

## Error Handling

### Error Response Format

All API errors return a consistent JSON structure:

```json
{
  "detail": "Human-readable error message"
}
```

### HTTP Status Codes

| Status Code | Description | When to Handle |
|-------------|-------------|----------------|
| `400` | Bad Request | Invalid request data, show error to user |
| `401` | Unauthorized | Token expired/invalid, redirect to login |
| `403` | Forbidden | User lacks permission, show access denied message |
| `404` | Not Found | Resource doesn't exist, show not found page |
| `409` | Conflict | Resource conflict (e.g., already purchased), show specific message |
| `500` | Server Error | Internal error, show generic error message |

### Common Error Scenarios

**Authentication Errors:**
```json
{
  "detail": "Authentication credentials were not provided."
}
```

**Permission Errors:**
```json
{
  "detail": "You do not have permission to perform this action."
}
```

**Content Already Purchased:**
```json
{
  "detail": "Article already purchased."
}
```

**Validation Errors:**
```json
{
  "detail": "Discussion title is required"
}
```

### Frontend Error Handling Tips

1. **401 Errors**: Automatically redirect to login page
2. **403 Errors**: Show user-friendly "access denied" message
3. **404 Errors**: Show "content not found" page
4. **409 Errors**: Display the specific conflict message to user
5. **500 Errors**: Show generic "something went wrong" message and optionally retry
