# WebSocket Message Replies Example

## WebSocket Message Structure

The WebSocket consumers now support creating both regular messages and replies through the same interface.

### Creating a Regular Message

**Send to WebSocket:**
```json
{
  "type": "message",
  "body": "Hello everyone!"
}
```

**Response from WebSocket:**
```json
{
  "type": "message",
  "message": {
    "id": "msg-001",
    "thread": "thread-uuid",
    "author": "user-123",
    "author_details": {
      "id": "user-123",
      "email": "<EMAIL>",
      "first_name": "<PERSON>",
      "last_name": "<PERSON><PERSON>",
      "nickname": "johndo<PERSON>",
      "picture": "https://example.com/photos/tiny/john.jpg"
    },
    "parent_message": null,
    "body": "Hello everyone!",
    "replies": [],
    "reply_count": 0,
    "created": "2024-01-15T10:00:00Z"
  }
}
```

### Creating a Reply

**Send to WebSocket:**
```json
{
  "type": "message",
  "body": "Hi <PERSON>!",
  "parent_message": "msg-001"
}
```

**Response from WebSocket:**
```json
{
  "type": "message",
  "message": {
    "id": "msg-002",
    "thread": "thread-uuid",
    "author": "user-456",
    "author_details": {
      "id": "user-456",
      "email": "<EMAIL>",
      "first_name": "Jane",
      "last_name": "Smith",
      "nickname": "janesmith",
      "picture": "https://example.com/photos/tiny/jane.jpg"
    },
    "parent_message": "msg-001",
    "body": "Hi John!",
    "replies": [],
    "reply_count": 0,
    "created": "2024-01-15T10:30:00Z"
  }
}
```

## JavaScript WebSocket Client Example

```javascript
class ChatWebSocket {
  constructor(threadId, token, isDiscussion = false) {
    this.threadId = threadId;
    this.token = token;
    this.isDiscussion = isDiscussion;
    this.ws = null;
  }

  connect() {
    const endpoint = this.isDiscussion ? 'discussions' : 'chat';
    const wsUrl = `wss://api.example.com/ws/${endpoint}/${this.threadId}/?token=${this.token}`;
    this.ws = new WebSocket(wsUrl);

    this.ws.onopen = () => {
      console.log('Connected to chat WebSocket');
    };

    this.ws.onmessage = (event) => {
      const data = JSON.parse(event.data);
      this.handleMessage(data);
    };

    this.ws.onclose = () => {
      console.log('Disconnected from chat WebSocket');
    };

    this.ws.onerror = (error) => {
      console.error('WebSocket error:', error);
    };
  }

  handleMessage(data) {
    switch (data.type) {
      case 'history':
        console.log('Received message history:', data.messages);
        this.displayMessages(data.messages);
        break;
      case 'message':
        console.log('Received new message:', data.message);
        this.displayMessage(data.message);
        break;
      case 'error':
        console.error('WebSocket error:', data.message);
        break;
    }
  }

  // Send a regular message
  sendMessage(body) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify({
        type: 'message',
        body: body
      }));
    }
  }

  // Send a reply to a message
  sendReply(body, parentMessageId) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify({
        type: 'message',
        body: body,
        parent_message: parentMessageId
      }));
    }
  }

  displayMessages(messages) {
    messages.forEach(message => this.displayMessage(message));
  }

  displayMessage(message) {
    const authorName = `${message.author_details.first_name} ${message.author_details.last_name}`;
    console.log(`${authorName}: ${message.body}`);

    // Display replies if any
    if (message.replies && message.replies.length > 0) {
      message.replies.forEach(reply => {
        const replyAuthor = `${reply.author_details.first_name} ${reply.author_details.last_name}`;
        console.log(`  └─ ${replyAuthor}: ${reply.body}`);
      });
    }
  }

  disconnect() {
    if (this.ws) {
      this.ws.close();
    }
  }
}

// Usage examples
// For chat
const chat = new ChatWebSocket('thread-uuid', 'your-jwt-token', false);
chat.connect();

// For discussion
const discussion = new ChatWebSocket('thread-uuid', 'your-jwt-token', true);
discussion.connect();

// Send a regular message
chat.sendMessage('Hello everyone!');
discussion.sendMessage('What do you think about this topic?');

// Send a reply
chat.sendReply('Hi there!', 'msg-001');
discussion.sendReply('Great point!', 'msg-001');
```

## React Component Example

```jsx
import React, { useState, useEffect, useRef } from 'react';

const ChatComponent = ({ threadId, token }) => {
  const [messages, setMessages] = useState([]);
  const [newMessage, setNewMessage] = useState('');
  const [replyingTo, setReplyingTo] = useState(null);
  const wsRef = useRef(null);

  useEffect(() => {
    // Connect to WebSocket
    const wsUrl = `wss://api.example.com/ws/chat/${threadId}/?token=${token}`;
    wsRef.current = new WebSocket(wsUrl);

    wsRef.current.onmessage = (event) => {
      const data = JSON.parse(event.data);

      if (data.type === 'history') {
        setMessages(data.messages);
      } else if (data.type === 'message') {
        setMessages(prev => [data.message, ...prev]);
      }
    };

    return () => {
      if (wsRef.current) {
        wsRef.current.close();
      }
    };
  }, [threadId, token]);

  const sendMessage = () => {
    if (!newMessage.trim()) return;

    const messageData = {
      type: 'message',
      body: newMessage
    };

    // Add parent_message if replying
    if (replyingTo) {
      messageData.parent_message = replyingTo.id;
    }

    wsRef.current.send(JSON.stringify(messageData));
    setNewMessage('');
    setReplyingTo(null);
  };

  const startReply = (message) => {
    setReplyingTo(message);
  };

  const cancelReply = () => {
    setReplyingTo(null);
  };

  return (
    <div className="chat-container">
      <div className="messages">
        {messages.map(message => (
          <div key={message.id} className="message">
            <div className="message-header">
              <img src={message.author_details.picture} alt="Avatar" />
              <span>{message.author_details.first_name} {message.author_details.last_name}</span>
              <button onClick={() => startReply(message)}>Reply</button>
            </div>
            <div className="message-body">{message.body}</div>

            {/* Display replies */}
            {message.replies && message.replies.length > 0 && (
              <div className="replies">
                {message.replies.map(reply => (
                  <div key={reply.id} className="reply">
                    <div className="reply-header">
                      <img src={reply.author_details.picture} alt="Avatar" />
                      <span>{reply.author_details.first_name} {reply.author_details.last_name}</span>
                    </div>
                    <div className="reply-body">{reply.body}</div>
                  </div>
                ))}
              </div>
            )}
          </div>
        ))}
      </div>

      <div className="message-input">
        {replyingTo && (
          <div className="replying-to">
            Replying to: {replyingTo.body}
            <button onClick={cancelReply}>Cancel</button>
          </div>
        )}
        <input
          type="text"
          value={newMessage}
          onChange={(e) => setNewMessage(e.target.value)}
          onKeyPress={(e) => e.key === 'Enter' && sendMessage()}
          placeholder={replyingTo ? "Type your reply..." : "Type a message..."}
        />
        <button onClick={sendMessage}>Send</button>
      </div>
    </div>
  );
};

export default ChatComponent;
```

## Key Features

1. **Automatic Thread Association**: Thread ID comes from WebSocket URL
2. **Reply Support**: Include `parent_message` field to create replies
3. **1-Level Nesting**: Replies to replies automatically become replies to root message
4. **Real-time Updates**: All participants see new messages and replies instantly
5. **Complete Author Info**: Every message includes full author details
6. **Nested Structure**: Replies are nested within their parent messages

## WebSocket Message Types

### Outgoing (Client → Server)
- **Regular Message**: `{ "type": "message", "body": "text" }`
- **Reply**: `{ "type": "message", "body": "text", "parent_message": "uuid" }`

### Incoming (Server → Client)
- **History**: `{ "type": "history", "messages": [...] }`
- **New Message**: `{ "type": "message", "message": {...} }`
- **Error**: `{ "type": "error", "message": "error text" }`
