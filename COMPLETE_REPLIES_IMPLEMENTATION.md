# Complete Message Replies Implementation

## Overview

This document aggregates all information about the message reply system implementation, including REST API endpoints, WebSocket integration, and usage examples for both chats and discussions.

## Key Features

### 1. Model Structure
- **Parent-Child Relationship**: Messages can have a `parent_message` field pointing to another message
- **1-Level Nesting**: Replies to replies automatically become replies to the root message
- **Hierarchical Grouping**: All replies are grouped under the original message
- **Chronological Sorting**: Replies sorted by creation date (oldest first)

### 2. Database Schema
```python
class ChatMessage(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    thread = models.ForeignKey(ChatThread, on_delete=models.CASCADE, related_name='messages')
    author = models.ForeignKey(User, on_delete=models.CASCADE, related_name='chat_messages')
    parent_message = models.ForeignKey(
        'self',
        on_delete=models.CASCADE,
        related_name='replies',
        null=True,
        blank=True,
        db_index=True
    )
    body = models.TextField()
    created = models.DateTimeField(auto_now_add=True, db_index=True)
    
    def get_root_message(self):
        """Get the root message for this message."""
        if self.parent_message is None:
            return self
        return self.parent_message
    
    def is_reply(self):
        """Check if this message is a reply."""
        return self.parent_message is not None
```

## REST API Endpoints

### Chat Messages
- **Get Messages**: `GET /api/chat/{thread_id}/messages/`
- **Create Message**: `POST /api/chat/{thread_id}/messages/`
- **Get Message**: `GET /api/chat/{thread_id}/messages/{message_id}/`
- **Get Replies**: `GET /api/chat/{thread_id}/messages/{message_id}/replies/`

### Discussion Messages
- **Get Messages**: `GET /api/discussions/{thread_id}/messages/`
- **Create Message**: `POST /api/discussions/{thread_id}/messages/`
- **Get Message**: `GET /api/discussions/{thread_id}/messages/{message_id}/`
- **Get Replies**: `GET /api/discussions/{thread_id}/messages/{message_id}/replies/`

### Request/Response Examples

#### Create a New Message
```http
POST /api/chat/{thread_id}/messages/
Content-Type: application/json

{
  "body": "This is a new message"
}
```

#### Create a Reply
```http
POST /api/chat/{thread_id}/messages/
Content-Type: application/json

{
  "parent_message": "message-uuid",
  "body": "This is a reply"
}
```

#### Response Structure
```json
{
  "id": "msg-001",
  "thread": "thread-uuid",
  "author": "user-123",
  "author_details": {
    "id": "user-123",
    "email": "<EMAIL>",
    "first_name": "John",
    "last_name": "Doe",
    "nickname": "johndoe",
    "picture": "https://example.com/photos/tiny/john.jpg"
  },
  "parent_message": null,
  "body": "Original message",
  "replies": [
    {
      "id": "msg-002",
      "thread": "thread-uuid",
      "author": "user-456",
      "author_details": {
        "id": "user-456",
        "email": "<EMAIL>",
        "first_name": "Jane",
        "last_name": "Smith",
        "nickname": "janesmith",
        "picture": "https://example.com/photos/tiny/jane.jpg"
      },
      "parent_message": "msg-001",
      "body": "This is a reply",
      "replies": [],
      "reply_count": 0,
      "created": "2024-01-15T10:30:00Z"
    }
  ],
  "reply_count": 1,
  "created": "2024-01-15T10:00:00Z"
}
```

## WebSocket Integration

### Connection URLs
- **Chat WebSocket**: `ws://localhost:8000/ws/chat/{thread_id}/?token=jwt_token`
- **Discussion WebSocket**: `ws://localhost:8000/ws/discussions/{thread_id}/?token=jwt_token`

### Message Format

#### Send a Regular Message
```json
{
  "type": "message",
  "body": "Hello everyone!"
}
```

#### Send a Reply
```json
{
  "type": "message",
  "body": "This is a reply",
  "parent_message": "message-uuid"
}
```

#### WebSocket Response
```json
{
  "type": "message",
  "message": {
    "id": "msg-001",
    "thread": "thread-uuid",
    "author": "user-123",
    "author_details": {
      "id": "user-123",
      "email": "<EMAIL>",
      "first_name": "John",
      "last_name": "Doe",
      "nickname": "johndoe",
      "picture": "https://example.com/photos/tiny/john.jpg"
    },
    "parent_message": null,
    "body": "Hello everyone!",
    "replies": [],
    "reply_count": 0,
    "created": "2024-01-15T10:00:00Z"
  }
}
```

## Implementation Details

### Backend Logic

#### 1-Level Nesting Enforcement
```python
def create(self, validated_data):
    # Handle reply logic - ensure only 1 level of nesting
    parent_message = validated_data.get('parent_message')
    if parent_message and parent_message.parent_message is not None:
        # If trying to reply to a reply, reply to the root message instead
        validated_data['parent_message'] = parent_message.parent_message
    
    return super().create(validated_data)
```

#### WebSocket Message Creation
```python
@database_sync_to_async
def create_message(self, body, parent_message_id=None):
    thread = ChatThread.objects.get(id=self.thread_id)
    
    # Handle parent message for replies
    parent_message = None
    if parent_message_id:
        try:
            parent_message = ChatMessage.objects.get(id=parent_message_id, thread=thread)
            # Ensure only 1 level of nesting
            if parent_message.parent_message is not None:
                parent_message = parent_message.parent_message
        except ChatMessage.DoesNotExist:
            parent_message = None
    
    message = ChatMessage.objects.create(
        thread=thread,
        author=self.user,
        body=body,
        parent_message=parent_message
    )
    return message
```

### Frontend Integration

#### JavaScript WebSocket Client
```javascript
class ChatWebSocket {
  constructor(threadId, token, isDiscussion = false) {
    this.threadId = threadId;
    this.token = token;
    this.isDiscussion = isDiscussion;
    this.ws = null;
  }

  connect() {
    const endpoint = this.isDiscussion ? 'discussions' : 'chat';
    const wsUrl = `wss://api.example.com/ws/${endpoint}/${this.threadId}/?token=${this.token}`;
    this.ws = new WebSocket(wsUrl);

    this.ws.onmessage = (event) => {
      const data = JSON.parse(event.data);
      this.handleMessage(data);
    };
  }

  // Send a regular message
  sendMessage(body) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify({
        type: 'message',
        body: body
      }));
    }
  }

  // Send a reply to a message
  sendReply(body, parentMessageId) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify({
        type: 'message',
        body: body,
        parent_message: parentMessageId
      }));
    }
  }
}
```

#### React Component Example
```jsx
const ChatComponent = ({ threadId, token, isDiscussion = false }) => {
  const [messages, setMessages] = useState([]);
  const [newMessage, setNewMessage] = useState('');
  const [replyingTo, setReplyingTo] = useState(null);
  const wsRef = useRef(null);

  useEffect(() => {
    const endpoint = isDiscussion ? 'discussions' : 'chat';
    const wsUrl = `wss://api.example.com/ws/${endpoint}/${threadId}/?token=${token}`;
    wsRef.current = new WebSocket(wsUrl);

    wsRef.current.onmessage = (event) => {
      const data = JSON.parse(event.data);
      
      if (data.type === 'history') {
        setMessages(data.messages);
      } else if (data.type === 'message') {
        setMessages(prev => [data.message, ...prev]);
      }
    };

    return () => {
      if (wsRef.current) {
        wsRef.current.close();
      }
    };
  }, [threadId, token, isDiscussion]);

  const sendMessage = () => {
    if (!newMessage.trim()) return;

    const messageData = {
      type: 'message',
      body: newMessage
    };

    // Add parent_message if replying
    if (replyingTo) {
      messageData.parent_message = replyingTo.id;
    }

    wsRef.current.send(JSON.stringify(messageData));
    setNewMessage('');
    setReplyingTo(null);
  };

  return (
    <div className="chat-container">
      {/* Message display and input components */}
    </div>
  );
};
```

#### REST API Client
```javascript
class MessageAPI {
  constructor(baseURL) {
    this.baseURL = baseURL;
  }

  // Get messages for chat
  async getChatMessages(threadId) {
    const response = await fetch(`${this.baseURL}/chat/${threadId}/messages/`);
    return response.json();
  }

  // Get messages for discussion
  async getDiscussionMessages(threadId) {
    const response = await fetch(`${this.baseURL}/discussions/${threadId}/messages/`);
    return response.json();
  }

  // Create a message
  async createMessage(threadId, body, isDiscussion = false) {
    const endpoint = isDiscussion ? 'discussions' : 'chat';
    const response = await fetch(`${this.baseURL}/${endpoint}/${threadId}/messages/`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ body })
    });
    return response.json();
  }

  // Create a reply
  async createReply(threadId, parentMessageId, body, isDiscussion = false) {
    const endpoint = isDiscussion ? 'discussions' : 'chat';
    const response = await fetch(`${this.baseURL}/${endpoint}/${threadId}/messages/`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ 
        parent_message: parentMessageId, 
        body 
      })
    });
    return response.json();
  }
}
```

## Key Benefits

1. **Unified System**: Same reply logic works for both chats and discussions
2. **Clean Hierarchy**: 1-level nesting prevents complex thread structures
3. **Real-time Updates**: WebSocket integration for instant reply notifications
4. **Complete Author Info**: Full user details included at every level
5. **RESTful Design**: Nested URLs with thread ID in path
6. **Flexible Frontend**: Easy to build threaded conversation UIs

## Message Types Summary

### WebSocket Messages (Client → Server)
- **Regular Message**: `{ "type": "message", "body": "text" }`
- **Reply**: `{ "type": "message", "body": "text", "parent_message": "uuid" }`

### WebSocket Messages (Server → Client)
- **History**: `{ "type": "history", "messages": [...] }`
- **New Message**: `{ "type": "message", "message": {...} }`
- **Error**: `{ "type": "error", "message": "error text" }`

### REST API Payloads
- **New Message**: `{ "body": "text" }`
- **Reply**: `{ "parent_message": "uuid", "body": "text" }`

## URL Patterns

### REST API
- Chat: `/api/chat/{thread_id}/messages/`
- Discussion: `/api/discussions/{thread_id}/messages/`

### WebSocket
- Chat: `ws://host/ws/chat/{thread_id}/?token=jwt`
- Discussion: `ws://host/ws/discussions/{thread_id}/?token=jwt`

This implementation provides a robust, scalable foundation for threaded conversations in both chat and discussion contexts, with consistent behavior across REST API and WebSocket interfaces.
