# Reply Author Details Example

## Complete Message Structure with Author Details

Every message and reply includes complete author information through the `author_details` field.

### Author Details Fields

Each `author_details` object includes:
- `id`: User's unique identifier
- `email`: User's email address
- `first_name`: User's first name
- `last_name`: User's last name
- `nickname`: User's nickname from profile
- `picture`: User's tiny profile photo URL

### Example API Response

```json
{
  "id": "msg-001",
  "thread": "thread-uuid",
  "author": "user-123",
  "author_details": {
    "id": "user-123",
    "email": "<EMAIL>",
    "first_name": "<PERSON>",
    "last_name": "<PERSON><PERSON>",
    "nickname": "johndo<PERSON>",
    "picture": "https://example.com/photos/tiny/john.jpg"
  },
  "parent_message": null,
  "body": "What do you think about the new feature?",
  "replies": [
    {
      "id": "msg-002",
      "thread": "thread-uuid",
      "author": "user-456",
      "author_details": {
        "id": "user-456",
        "email": "<EMAIL>",
        "first_name": "<PERSON>",
        "last_name": "<PERSON>",
        "nickname": "janesmith",
        "picture": "https://example.com/photos/tiny/jane.jpg"
      },
      "parent_message": "msg-001",
      "body": "I love it! Very intuitive design.",
      "replies": [
        {
          "id": "msg-003",
          "thread": "thread-uuid",
          "author": "user-789",
          "author_details": {
            "id": "user-789",
            "email": "<EMAIL>",
            "first_name": "Bob",
            "last_name": "Johnson",
            "nickname": "bobjohnson",
            "picture": "https://example.com/photos/tiny/bob.jpg"
          },
          "parent_message": "msg-002",
          "body": "Agreed! The UX is fantastic.",
          "replies": [],
          "reply_count": 0,
          "created": "2024-01-15T10:45:00Z"
        }
      ],
      "reply_count": 1,
      "created": "2024-01-15T10:30:00Z"
    }
  ],
  "reply_count": 1,
  "created": "2024-01-15T10:00:00Z"
}
```

### Frontend Usage

```javascript
// Display message with author info
function displayMessage(message) {
  const authorName = `${message.author_details.first_name} ${message.author_details.last_name}`;
  const authorNickname = message.author_details.nickname;
  const authorPicture = message.author_details.picture;

  console.log(`${authorName} (@${authorNickname}): ${message.body}`);

  // Display replies with their author info
  message.replies.forEach(reply => {
    const replyAuthor = `${reply.author_details.first_name} ${reply.author_details.last_name}`;
    console.log(`  └─ ${replyAuthor}: ${reply.body}`);

    // Display nested replies
    reply.replies.forEach(nestedReply => {
      const nestedAuthor = `${nestedReply.author_details.first_name} ${nestedReply.author_details.last_name}`;
      console.log(`    └─ ${nestedAuthor}: ${nestedReply.body}`);
    });
  });
}

// API Usage Examples
class MessageAPI {
  // Create message in chat
  async createChatMessage(threadId, body) {
    return fetch(`/api/chat/${threadId}/messages/`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ body })
    });
  }

  // Create message in discussion
  async createDiscussionMessage(threadId, body) {
    return fetch(`/api/discussions/${threadId}/messages/`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ body })
    });
  }

  // Create reply (works for both chats and discussions)
  async createReply(threadId, parentMessageId, body, isDiscussion = false) {
    const endpoint = isDiscussion ? 'discussions' : 'chat';
    return fetch(`/api/${endpoint}/${threadId}/messages/`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        parent_message: parentMessageId,
        body
      })
    });
  }
}
```

### Benefits

1. **Complete User Info**: Every message includes full author details
2. **Profile Integration**: Includes nickname and profile photo
3. **Consistent Structure**: Same author details format at every nesting level
4. **Frontend Ready**: All data needed for rich user interfaces
5. **No Additional Requests**: Author details included in single API call
