# Discussions Implementation

## Overview

This implementation adds Discussions functionality to the existing discourse app by reusing the ChatThread model with a `discourse_type` field to differentiate between 'chats' and 'discussions'. The approach allows for code reuse while providing distinct functionality for each type.

## Key Features

### 1. Model Changes
- Added `discourse_type` field with choices: 'chats' and 'discussions'
- Added `description` field (Cha<PERSON><PERSON><PERSON>, max_length=80, blank=True, null=True) for optional thread descriptions
- Default value is 'chats' for backward compatibility
- Added proper field constraints and choices

### 2. API Endpoints
- **Chat Threads**: `/api/chat/threads/`
- **Chat Messages**: `/api/chat/{thread_id}/messages/`
- **Discussion Threads**: `/api/discussions/threads/`
- **Discussion Messages**: `/api/discussions/{thread_id}/messages/`
- Both use the same ViewSets but with different basenames for differentiation
- Nested URL structure with thread ID in path

### 3. WebSocket Endpoints
- **Chats**: `ws/chat/` and `ws/chat/{thread_id}/`
- **Discussions**: `ws/discussions/` and `ws/discussions/{thread_id}/`
- Reuses the same consumers with path-based filtering
- Supports message replies with `parent_message` field

### 4. Functionality Differences

#### Chats
- Private/public messaging
- Requires participants for access
- Auto-generated titles hidden for private chats
- Traditional instant messaging behavior
- Titles are optional (auto-generated if not provided)

#### Discussions
- Forum-like functionality
- Public discussions accessible to all authenticated users
- Private discussions only accessible to participants
- Can be created with only 1 participant (the creator)
- **Titles are mandatory** - raises `InvalidRequest` if not provided
- All titles are visible (no hiding)

### 5. Title Generation

#### Chats
- **Private**: "Private Chat: {first 8 UUID chars}" (hidden from frontend)
- **Public**: Participant names (e.g., "John Doe and Jane Smith")

#### Discussions
- **Titles are required** - validation at view level raises `InvalidRequest('Discussion title is required')` if missing
- Fallback auto-generation exists in serializer but shouldn't be reached due to validation

### 6. Permissions

#### Chats
- Private: Only participants can access
- Public: All authenticated users can view, only participants can modify

#### Discussions
- Private: Only participants can access
- Public: All authenticated users can view and participate

### 7. Discussion List Ordering

When fetching the discussion list, threads are ordered with a specific priority:

1. **Participant Threads First** (priority = 0): Discussions where the user is a participant
2. **Public Non-Participant Threads** (priority = 1): Public discussions where the user is not a participant

Within each priority group, threads are ordered by creation date (newest first).

**Example ordering for User B:**
- User B's own discussion (participant, newest)
- Discussion A where User B was added as participant (participant, older)
- Public Discussion C (non-participant, newest)
- Public Discussion D (non-participant, older)

## Implementation Details

### Files Modified

1. **`models.py`**: Added discourse_type field with choices
2. **`views.py`**: Updated queryset filtering based on basename + **title validation for discussions**
3. **`serializers.py`**: Added discourse_type handling and title generation logic
4. **`consumers.py`**: Updated WebSocket filtering and permissions
5. **`routing.py`**: Added discussion WebSocket routes
6. **`permissions.py`**: Updated permissions for discussion access
7. **`api.py`**: Already had discussion endpoints registered

### Migration
- Created migration to update discourse_type field with proper choices

### Testing
- Added comprehensive test suite in `tests_discussions.py`

## Usage Examples

### Creating a Private Discussion
```python
POST /api/discussions/threads/
{
    "title": "My Private Discussion",
    "is_private": true
}
```

### Creating a Public Discussion
```python
POST /api/discussions/threads/
{
    "title": "Public Discussion Topic",
    "is_private": false
}
```

### WebSocket Connection
```javascript
// For discussions
const ws = new WebSocket('wss://api.example.com/ws/discussions/');

// For specific discussion thread
const threadWs = new WebSocket('wss://api.example.com/ws/discussions/{thread_id}/');
```

## Key Benefits

1. **Code Reuse**: Leverages existing chat infrastructure
2. **Clean Separation**: Clear distinction between chats and discussions
3. **Scalable**: Easy to extend with additional discourse types
4. **Backward Compatible**: Existing chats continue to work unchanged
5. **Flexible Permissions**: Different access patterns for different use cases

## Future Enhancements

1. **Thread Categories**: Add categorization for discussions
2. **Moderation**: Add moderation features for public discussions
3. **Reactions**: Add reaction/voting system for discussion messages
4. **Search**: Enhanced search functionality for discussions
5. **Notifications**: Specialized notification patterns for discussions
