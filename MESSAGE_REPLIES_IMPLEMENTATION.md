# Message Replies Implementation

## Overview

This implementation adds a reply system to chat and discussion messages with 1-level nesting. Users can reply to messages, and all replies are grouped under the original (root) message, sorted by creation date.

## Key Features

### 1. Model Changes
- Added `parent_message` field to `ChatMessage` model
- Self-referencing Foreign<PERSON>ey with `related_name='replies'`
- Supports null/blank for root messages
- Added helper methods: `get_root_message()` and `is_reply()`

### 2. Reply Logic
- **1-Level Nesting Only**: Replies to replies automatically become replies to the root message
- **Root Message Grouping**: All replies are grouped under the original message
- **Sorted by Creation**: Replies are always sorted by creation date (oldest first)

### 3. API Endpoints

#### Get Messages with Nested Replies
```http
GET /api/chat/{thread_id}/messages/
GET /api/discussions/{thread_id}/messages/
```
Returns root messages with all replies nested hierarchically.

#### Get Replies for a Message
```http
GET /api/chat/{thread_id}/messages/{message_id}/replies/
GET /api/discussions/{thread_id}/messages/{message_id}/replies/
```
Returns all replies for a specific message.

#### Create a Message
```http
POST /api/chat/{thread_id}/messages/
POST /api/discussions/{thread_id}/messages/
{
  "body": "This is a new message"
}
```

#### Create a Reply
```http
POST /api/chat/{thread_id}/messages/
POST /api/discussions/{thread_id}/messages/
{
  "parent_message": "message-uuid",
  "body": "This is a reply"
}
```

## Data Structure

### Root Message with Nested Replies Response
```json
{
  "id": "msg-001",
  "thread": "thread-uuid",
  "author": "user-123",
  "author_details": {
    "id": "user-123",
    "email": "<EMAIL>",
    "first_name": "John",
    "last_name": "Doe",
    "nickname": "johndoe",
    "picture": "https://example.com/photos/tiny/john.jpg"
  },
  "parent_message": null,
  "body": "This is the original message",
  "replies": [
    {
      "id": "msg-002",
      "thread": "thread-uuid",
      "author": "user-456",
      "author_details": {
        "id": "user-456",
        "email": "<EMAIL>",
        "first_name": "Jane",
        "last_name": "Smith",
        "nickname": "janesmith",
        "picture": "https://example.com/photos/tiny/jane.jpg"
      },
      "parent_message": "msg-001",
      "body": "This is a reply",
      "replies": [
        {
          "id": "msg-004",
          "thread": "thread-uuid",
          "author": "user-789",
          "author_details": {
            "id": "user-789",
            "email": "<EMAIL>",
            "first_name": "Bob",
            "last_name": "Johnson",
            "nickname": "bobjohnson",
            "picture": "https://example.com/photos/tiny/bob.jpg"
          },
          "parent_message": "msg-002",
          "body": "Reply to the reply",
          "replies": [],
          "reply_count": 0,
          "created": "2024-01-15T10:40:00Z"
        }
      ],
      "reply_count": 1,
      "created": "2024-01-15T10:30:00Z"
    },
    {
      "id": "msg-003",
      "thread": "thread-uuid",
      "author": "user-123",
      "author_details": {
        "id": "user-123",
        "email": "<EMAIL>",
        "first_name": "John",
        "last_name": "Doe",
        "nickname": "johndoe",
        "picture": "https://example.com/photos/tiny/john.jpg"
      },
      "parent_message": "msg-001",
      "body": "Another reply",
      "replies": [],
      "reply_count": 0,
      "created": "2024-01-15T10:35:00Z"
    }
  ],
  "reply_count": 2,
  "created": "2024-01-15T10:00:00Z"
}
```

### Reply Message Response
```json
{
  "id": "msg-002",
  "thread": "thread-uuid",
  "author": "user-456",
  "author_details": {
    "id": "user-456",
    "email": "<EMAIL>",
    "first_name": "Jane",
    "last_name": "Smith",
    "nickname": "janesmith",
    "picture": "https://example.com/photos/tiny/jane.jpg"
  },
  "parent_message": "msg-001",
  "body": "This is a reply",
  "replies": [],
  "reply_count": 0,
  "created": "2024-01-15T10:30:00Z"
}
```

## Implementation Details

### Model Methods

```python
def get_root_message(self):
    """Get the root message for this message."""
    if self.parent_message is None:
        return self
    return self.parent_message

def is_reply(self):
    """Check if this message is a reply."""
    return self.parent_message is not None
```

### Serializer Logic

1. **Root Messages**: Include `replies` array with nested reply data
2. **Reply Messages**: Empty `replies` array, but show `reply_count` of root message
3. **Reply Creation**: Automatically redirects replies-to-replies to the root message

### Query Optimization

- **Default Behavior**: Only fetch root messages (`parent_message__isnull=True`)
- **Include Replies**: Use `?include_replies=true` to get all messages
- **Prefetch Related**: Optimized queries with `select_related` for authors

## Usage Examples

### Creating a Thread Message
```javascript
// Create original message
const message = await fetch('/api/chat/thread-uuid/messages/', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    body: 'Hello everyone!'
  })
});
```

### Replying to a Message
```javascript
// Reply to the message
const reply = await fetch('/api/chat/thread-uuid/messages/', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    parent_message: 'msg-001',
    body: 'Hi there!'
  })
});
```

### Fetching Messages with Replies
```javascript
// Get root messages with all replies nested hierarchically
const messages = await fetch('/api/chat/thread-uuid/messages/');

// Get replies for specific message
const replies = await fetch('/api/chat/thread-uuid/messages/msg-001/replies/');
```

## Benefits

1. **Clean Structure**: Clear hierarchy with root messages and replies
2. **No Deep Nesting**: Prevents complex nested reply chains
3. **Sorted Replies**: Chronological order makes conversations easy to follow
4. **Flexible Queries**: Can fetch with or without replies based on UI needs
5. **Optimized Performance**: Efficient database queries with proper indexing

## Frontend Integration

### Message Display
- Show root messages in main timeline
- Display replies nested under each root message
- Sort replies chronologically (oldest first)
- Show reply count for each root message

### Reply Interface
- "Reply" button on each message
- Reply form appears inline or in modal
- Visual indication of which message is being replied to
- Prevent deep nesting in UI

### Real-time Updates
- WebSocket messages include `parent_message` field
- Frontend can update appropriate message thread
- New replies appear in correct position chronologically
