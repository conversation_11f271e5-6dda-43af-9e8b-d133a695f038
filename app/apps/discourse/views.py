from rest_framework import (
    filters,
    permissions,
    status,
    viewsets,
)
from rest_framework.response import Response
from rest_framework.decorators import action, api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
import asyncio
import uuid
from urllib.parse import urljoin, urlparse

from django.contrib.auth import get_user_model
from django.core.cache import cache
from django.db.models import Prefetch, Q
from django.db import models
from django.utils.decorators import method_decorator
from django.views.decorators.cache import cache_page
from django.utils import timezone
from django.utils.text import slugify

from apps.discourse.models import (
    ChatMessage,
    ChatThread,
    ThreadParticipant,
)
from apps.discourse.serializers import (
    ChatMessageSerializer,
    ChatThreadSerializer,
)
from apps.discourse.permissions import (
    IsMessageThreadParticipant,
    IsThreadParticipant,
)
from apps.discourse.cache import ChatCacheManager
from config.exceptions import InvalidRequest


User = get_user_model()


def upload_discourse_attachment(request, thread_id, user):
    """
    Upload an image attachment for discourse messages.
    Similar to upload_profile_image but for discourse attachments.
    """
    if 'file' not in request.FILES:
        raise InvalidRequest(detail='No file provided')

    image_file = request.FILES['file']

    # Validate file type (only images allowed)
    if not image_file.content_type.startswith('image/'):
        raise InvalidRequest(detail='Only image files are allowed')

    # Generate unique filename using the specified format
    image_filename = image_file.name.lower()
    image_file.name = f"{thread_id}_{str(user.id)[:8]}_{image_filename}"

    # Create a temporary ChatMessage to save the attachment
    # We'll use the storage system but return just the URL
    temp_message = ChatMessage(attachment=image_file)
    temp_message.attachment.save(image_file.name, image_file, save=False)

    # Generate the attachment URL
    picture_url = urljoin(temp_message.attachment.url, urlparse(temp_message.attachment.url).path)

    return picture_url


class ChatThreadViewSet(viewsets.ModelViewSet):
    """
    API endpoint for chat threads.
    """
    serializer_class = ChatThreadSerializer
    permission_classes = [permissions.IsAuthenticated]
    lookup_field = 'id'

    def get_queryset(self):
        user = self.request.user

        # Check if public threads should be excluded (default: include public threads)
        exclude_public = self.request.query_params.get('exclude_public', False)

        if self.basename == 'chat_threads':
            if exclude_public:
                # Only show threads where user is a participant (private threads only)
                queryset = ChatThread.objects.filter(
                    discourse_type='chats', participants=user)
            else:
                # Show both participant threads and public threads (default behavior)
                queryset = ChatThread.objects.filter(
                    discourse_type='chats'
                ).filter(
                    models.Q(is_private=False) | models.Q(participants=user)
                ).distinct()

        elif self.basename == 'discussion_threads':
            # For discussions, show participant threads first, then all public discussions
            from django.db.models import Case, When, Value, IntegerField

            if exclude_public:
                # Only show threads where user is a participant
                queryset = ChatThread.objects.filter(
                    discourse_type='discussions', participants=user)
            else:
                # Show both participant threads and public threads (default behavior)
                queryset = ChatThread.objects.filter(
                    discourse_type='discussions'
                ).filter(
                    models.Q(is_private=False) | models.Q(participants=user)
                ).distinct()

            # Add priority annotation for discussions (regardless of exclude_public)
            queryset = queryset.annotate(
                # Add ordering priority: 0 for participant threads, 1 for public non-participant threads
                priority=Case(
                    When(participants=user, then=Value(0)),
                    default=Value(1),
                    output_field=IntegerField()
                )
            )
        else:
            # Fallback for any other basename - include public threads by default
            if exclude_public:
                queryset = ChatThread.objects.filter(participants=user)
            else:
                queryset = ChatThread.objects.filter(
                    models.Q(is_private=False) | models.Q(participants=user)
                ).distinct()

        queryset = queryset.prefetch_related(
            'participants',
            Prefetch(
                'messages',
                queryset=ChatMessage.objects.order_by('-created')[:1],
                to_attr='last_messages'
            )
        ).annotate(
            messages_count_annotated=models.Count('messages')
        )

        # Apply ordering based on discourse type
        if self.basename == 'discussion_threads':
            # For discussions: participant threads first, then by creation date
            queryset = queryset.order_by('priority', '-created')
        else:
            # For chats: just by creation date
            queryset = queryset.order_by('-created')

        return queryset

    def list(self, request, *args, **kwargs):
        """
        List threads with caching support for thread lists including messages_count.
        """
        user = request.user
        exclude_public = request.query_params.get('exclude_public', False)

        # Determine discourse type from basename
        if self.basename == 'chat_threads':
            discourse_type = 'chats'
        elif self.basename == 'discussion_threads':
            discourse_type = 'discussions'
        else:
            discourse_type = 'chats'  # fallback

        # Try to get from cache first
        cached_threads = ChatCacheManager.get_thread_list(
            user.id, discourse_type, exclude_public
        )
        if cached_threads is not None:
            return Response(cached_threads)

        # If not cached, get from database
        queryset = self.filter_queryset(self.get_queryset())
        page = self.paginate_queryset(queryset)

        if page is not None:
            serializer = self.get_serializer(page, many=True)
            paginated_response = self.get_paginated_response(serializer.data)

            # Cache the results if it's the first page
            if hasattr(self, 'paginator') and getattr(self.paginator, 'offset', 0) == 0:
                ChatCacheManager.cache_thread_list(
                    user.id, discourse_type, paginated_response.data, exclude_public
                )

            return paginated_response

        serializer = self.get_serializer(queryset, many=True)
        response_data = serializer.data

        # Cache the results
        ChatCacheManager.cache_thread_list(
            user.id, discourse_type, response_data, exclude_public
        )

        return Response(response_data)

    def get_permissions(self):
        is_thread_participant = ['retrieve',
                                 'update',
                                 'partial_update',
                                 'destroy']
        if self.action in is_thread_participant:
            if self.basename == 'chat_threads':
                # Chats require participant permission
                return [permissions.IsAuthenticated(), IsThreadParticipant()]
            elif self.basename == 'discussion_threads':
                # Discussions: public ones are viewable by all, private ones require participation
                return [permissions.IsAuthenticated()]
        return super().get_permissions()

    def create(self, request, *args, **kwargs):
        """
        Create a new thread with validation for discussions.
        For chats between 2 participants, check if thread already exists.
        """
        # Validate discussion title requirement
        if self.basename == 'discussion_threads':
            title = request.data.get('title', '').strip()
            if not title:
                raise InvalidRequest(detail='Discussion title is required')

        # For chats, check if thread already exists between 2 participants
        if self.basename == 'chat_threads':
            participants = request.data.get('participants', [])
            current_user = request.user

            # If participants list includes current user and one other user (total 2)
            if len(participants) == 2 and current_user.id in participants:
                other_user_id = None
                for participant_id in participants:
                    if participant_id != current_user.id:
                        other_user_id = participant_id
                        break

                if other_user_id:
                    # Check if a chat thread already exists between these 2 users
                    existing_thread = ChatThread.objects.filter(
                        discourse_type='chats',
                        participants=current_user).filter(
                        participants=other_user_id).annotate(
                        participant_count=models.Count('participants')).filter(
                        participant_count=2).first()

                    if existing_thread:
                        # Return the existing thread instead of creating a new one
                        serializer = self.get_serializer(existing_thread)
                        return Response(serializer.data, status=status.HTTP_200_OK)

            # If only 1 participant provided (just current user), check if they meant to include themselves
            elif len(participants) == 1 and participants[0] != current_user.id:
                # User provided one other participant, add current user and check for existing thread
                other_user_id = participants[0]
                existing_thread = ChatThread.objects.filter(
                    discourse_type='chats',
                    participants=current_user).filter(
                    participants=other_user_id).annotate(
                    participant_count=models.Count('participants')).filter(
                    participant_count=2).first()

                if existing_thread:
                    # Return the existing thread instead of creating a new one
                    serializer = self.get_serializer(existing_thread)
                    return Response(serializer.data, status=status.HTTP_200_OK)

        response = super().create(request, *args, **kwargs)

        # Invalidate thread list caches for participants since a new thread was created
        if response.status_code == 201:
            try:
                thread_id = response.data.get('id')
                if thread_id:
                    thread = ChatThread.objects.get(id=thread_id)
                    ChatCacheManager.invalidate_thread_participants_cache(thread)
            except Exception as e:
                import logging
                logger = logging.getLogger(__name__)
                logger.warning(f"Error invalidating cache after thread creation: {e}")

        return response

    @action(detail=True, methods=['POST'])
    def upload_attachment(self, request, id=None):
        """
        Upload an image attachment for messages in this thread.
        Returns the attachment URL that can be used when creating messages.
        """
        thread = self.get_object()

        try:
            attachment_url = upload_discourse_attachment(request, thread.id, request.user)
            return Response({
                'attachment_url': attachment_url,
                'message': 'Attachment uploaded successfully'
            }, status=status.HTTP_201_CREATED)
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Error uploading attachment: {e}")
            raise InvalidRequest(detail='Failed to upload attachment')

    @action(detail=True, methods=['get'])
    def messages(self, request, id=None):
        """
        Get messages for a specific thread with pagination.
        """
        thread = self.get_object()

        # Get pagination parameters
        limit = int(request.query_params.get('limit', 30))
        offset = int(request.query_params.get('offset', 0))

        # Try to get from cache for first page
        if offset == 0:
            cached_messages = ChatCacheManager.get_thread_messages(thread.id, limit)
            if cached_messages is not None:
                total_count = ChatMessage.objects.filter(thread=thread).count()
                return Response({
                    'results': cached_messages,
                    'total': total_count,
                    'limit': limit,
                    'offset': offset,
                })

        # Get messages from database with pagination
        messages = ChatMessage.objects.filter(thread=thread).order_by('-created')[offset:offset+limit]
        serializer = ChatMessageSerializer(messages, many=True)

        if offset == 0 and messages:
            ChatCacheManager.cache_thread_messages(thread.id, messages)

        total_count = ChatMessage.objects.filter(thread=thread).count()
        return Response({
            'results': serializer.data,
            'total': total_count,
            'limit': limit,
            'offset': offset,
        })

    @action(detail=True, methods=['post'])
    def add_participant(self, request, id=None):
        thread = self.get_object()
        user_id = request.data.get('user_id')

        try:
            user = User.objects.get(id=user_id)
            if user in thread.participants.all():
                return Response(
                    {'detail': 'User is already a participant.'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            ThreadParticipant.objects.create(thread=thread, user=user)
            return Response(
                {'detail': 'User added successfully.'},
                status=status.HTTP_200_OK
            )
        except User.DoesNotExist:
            return Response(
                {'detail': 'User not found.'},
                status=status.HTTP_404_NOT_FOUND
            )

    @action(detail=True, methods=['post'])
    def remove_participant(self, request, id=None):
        thread = self.get_object()
        user_id = request.data.get('user_id')

        try:
            user = User.objects.get(id=user_id)
            if user == request.user and thread.participants.count() <= 1:
                return Response(
                    {'detail': 'Cannot remove yourself as the only participant.'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            participant = ThreadParticipant.objects.filter(thread=thread, user=user)
            if participant.exists():
                participant.delete()
                return Response(
                    {'detail': 'User removed successfully.'},
                    status=status.HTTP_200_OK
                )
            else:
                return Response(
                    {'detail': 'User is not a participant.'},
                    status=status.HTTP_400_BAD_REQUEST
                )
        except User.DoesNotExist:
            return Response(
                {'detail': 'User not found.'},
                status=status.HTTP_404_NOT_FOUND
            )

    @action(detail=True, methods=['POST'])
    def accept_request(self, request, id=None):
        """
        Accept a pending chat request.
        Only the receiver (not the creator) can accept the request.
        """
        thread = self.get_object()
        current_user = request.user

        # Check if thread is actually pending
        if not thread.pending_request:
            return Response(
                {'detail': 'This chat thread is not pending.'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Check if user is the receiver (not the creator)
        if current_user == thread.created_by:
            return Response(
                {'detail': 'You cannot accept your own chat request.'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Check if user is a participant
        if not thread.participants.filter(id=current_user.id).exists():
            return Response(
                {'detail': 'You are not a participant in this chat.'},
                status=status.HTTP_403_FORBIDDEN
            )

        # Accept the request
        thread.accept_request()

        # Invalidate caches
        ChatCacheManager.invalidate_thread_participants_cache(thread)

        import logging
        logger = logging.getLogger(__name__)
        logger.info(f"Chat request accepted by user {current_user.id} in thread {thread.id}")

        return Response(
            {'detail': 'Chat request accepted successfully.'},
            status=status.HTTP_200_OK
        )

    @action(detail=True, methods=['POST'])
    def reject_request(self, request, id=None):
        """
        Reject a pending chat request.
        Only the receiver (not the creator) can reject the request.
        This will delete the thread.
        """
        thread = self.get_object()
        current_user = request.user

        # Check if thread is actually pending
        if not thread.pending_request:
            return Response(
                {'detail': 'This chat thread is not pending.'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Check if user is the receiver (not the creator)
        if current_user == thread.created_by:
            return Response(
                {'detail': 'You cannot reject your own chat request.'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Check if user is a participant
        if not thread.participants.filter(id=current_user.id).exists():
            return Response(
                {'detail': 'You are not a participant in this chat.'},
                status=status.HTTP_403_FORBIDDEN
            )

        # Store thread info for logging before deletion
        thread_id = thread.id
        creator_id = thread.created_by.id if thread.created_by else None

        # Reject the request (this deletes the thread)
        thread.reject_request()

        import logging
        logger = logging.getLogger(__name__)
        logger.info(f"Chat request rejected by user {current_user.id} in thread {thread_id} (creator: {creator_id})")

        return Response(
            {'detail': 'Chat request rejected successfully.'},
            status=status.HTTP_200_OK
        )


class ChatMessageViewSet(viewsets.ModelViewSet):
    """
    API endpoint for chat and discussion messages.
    """
    serializer_class = ChatMessageSerializer
    permission_classes = [permissions.IsAuthenticated, IsMessageThreadParticipant]

    def get_queryset(self):
        queryset = ChatMessage.objects.select_related('author', 'thread')

        # Get thread_id from URL parameter
        thread_id = self.kwargs.get('thread_id')
        if thread_id:
            # Always show only root messages (replies will be nested in the serializer)
            queryset = queryset.filter(parent_message__isnull=True)

            limit = int(self.request.query_params.get('limit', 30))
            offset = int(self.request.query_params.get('offset', 0))

            if offset == 0:
                cached_messages = ChatCacheManager.get_thread_messages(thread_id, limit)
                if cached_messages is not None:
                    return ChatMessage.objects.none()

            queryset = queryset.filter(thread_id=thread_id).order_by('-created')

            if offset == 0:
                messages = queryset[:limit]
                if messages:
                    ChatCacheManager.cache_thread_messages(thread_id, messages)

        return queryset

    @action(detail=True, methods=['get'])
    def replies(self, request, pk=None):
        """
        Get all replies for a specific message.
        """
        message = self.get_object()

        # Get the root message (in case this is already a reply)
        root_message = message.get_root_message()

        # Get all replies to the root message, sorted by creation date
        replies = root_message.replies.all().order_by('created')

        serializer = ChatMessageReplySerializer(replies, many=True, context={'request': request})
        return Response(serializer.data)

    def create(self, request, *args, **kwargs):
        # Get thread_id from URL parameter
        thread_id = self.kwargs.get('thread_id')
        if not thread_id:
            raise InvalidRequest(detail='Thread ID is required in URL')

        # Verify thread exists and user has access
        try:
            thread = ChatThread.objects.get(id=thread_id)
        except ChatThread.DoesNotExist:
            raise InvalidRequest(detail='Thread not found')

        # For discussions, ensure user is enrolled before creating message
        if thread.discourse_type == 'discussions':
            # Check if user is already a participant
            if not thread.participants.filter(id=request.user.id).exists():
                # Try to auto-enroll user based on content permissions
                from apps.authentication.permissions import auto_enroll_user_in_discussion
                if not auto_enroll_user_in_discussion(request.user, str(thread_id)):
                    raise InvalidRequest(detail='You do not have permission to post in this discussion')

        # Add thread to request data
        data = request.data.copy()
        data['thread'] = thread_id

        serializer = self.get_serializer(data=data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        headers = self.get_success_headers(serializer.data)

        message = ChatMessage.objects.get(id=serializer.data['id'])
        ChatCacheManager.cache_message(message)

        # Note: cache_message already invalidates thread list caches for participants
        # since the message count has changed

        return Response(
            serializer.data, status=status.HTTP_201_CREATED, headers=headers)

    def list(self, request, *args, **kwargs):
        # Get thread_id from URL parameter
        thread_id = self.kwargs.get('thread_id')
        if thread_id:
            limit = int(request.query_params.get('limit', 30))
            offset = int(request.query_params.get('offset', 0))

            # Only use cache for the first page
            if offset == 0:
                # Try to get cached messages
                cached_messages = ChatCacheManager.get_thread_messages(thread_id, limit)
                if cached_messages is not None:
                    # Calculate total count for pagination info
                    total_count = ChatMessage.objects.filter(thread_id=thread_id).count()

                    # Create a paginated response
                    return Response({
                        'results': cached_messages,
                        'total': total_count,
                        'limit': limit,
                        'offset': offset,
                    })

            # Get messages from database with pagination
            queryset = ChatMessage.objects.filter(thread_id=thread_id).order_by('-created')[offset:offset+limit]
            serializer = self.get_serializer(queryset, many=True)

            if offset == 0 and queryset:
                ChatCacheManager.cache_thread_messages(thread_id, queryset)

            total_count = ChatMessage.objects.filter(thread_id=thread_id).count()
            return Response({
                'results': serializer.data,
                'total': total_count,
                'limit': limit,
                'offset': offset,
            })

        # Fall back to database if not cached
        return super().list(request, *args, **kwargs)


@api_view(['GET'])
def websocket_health(request):
    """
    WebSocket health check endpoint
    """
    try:
        from channels.layers import get_channel_layer
        from django.conf import settings
        import logging

        logger = logging.getLogger(__name__)
        checks = {}

        # Check channel layer configuration
        try:
            channel_layer = get_channel_layer()
            if channel_layer:
                checks['channel_layer'] = {
                    'healthy': True,
                    'message': f"Channel layer configured: {channel_layer.__class__.__name__}"
                }

                # Check if it's InMemory or Redis (without testing connection)
                if 'InMemory' in channel_layer.__class__.__name__:
                    checks['redis_connection'] = {
                        'healthy': True,
                        'message': 'Using InMemoryChannelLayer (Redis not required)'
                    }
                else:
                    checks['redis_connection'] = {
                        'healthy': True,
                        'message': f'Redis channel layer configured (connection not tested during health check)'
                    }
            else:
                checks['channel_layer'] = {
                    'healthy': False,
                    'message': "Channel layer not configured"
                }
        except Exception as e:
            checks['channel_layer'] = {
                'healthy': False,
                'message': f"Channel layer error: {str(e)}"
            }

        # Check Redis URL configuration
        redis_url = getattr(settings, 'REDIS_CHANNELS_URL', None)
        if redis_url:
            checks['redis_config'] = {
                'healthy': True,
                'message': f"Redis URL configured: {redis_url[:20]}..."
            }
        else:
            checks['redis_config'] = {
                'healthy': False,
                'message': "REDIS_CHANNELS_URL not configured"
            }

        # Check ASGI application
        asgi_app = getattr(settings, 'ASGI_APPLICATION', None)
        if asgi_app:
            checks['asgi_config'] = {
                'healthy': True,
                'message': f"ASGI application configured: {asgi_app}"
            }
        else:
            checks['asgi_config'] = {
                'healthy': False,
                'message': "ASGI_APPLICATION not configured"
            }

        # Check channel layers settings
        channel_layers_config = getattr(settings, 'CHANNEL_LAYERS', {})
        if channel_layers_config:
            checks['channel_layers_config'] = {
                'healthy': True,
                'message': f"Channel layers configured with backend: {channel_layers_config.get('default', {}).get('BACKEND', 'Unknown')}"
            }
        else:
            checks['channel_layers_config'] = {
                'healthy': False,
                'message': "CHANNEL_LAYERS not configured"
            }

        overall_healthy = all(check['healthy'] for check in checks.values())

        health_result = {
            'healthy': overall_healthy,
            'checks': checks,
            'timestamp': str(timezone.now()) if 'timezone' in globals() else 'unknown'
        }

        status_code = status.HTTP_200_OK if health_result['healthy'] else status.HTTP_503_SERVICE_UNAVAILABLE

        return Response(health_result, status=status_code)

    except Exception as e:
        import traceback
        return Response({
            'healthy': False,
            'error': str(e),
            'traceback': traceback.format_exc()
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)





@api_view(['GET'])
def basic_health_check(request):
    """
    Basic health check endpoint that doesn't depend on external services
    """
    try:
        from django.conf import settings
        import django

        health_info = {
            'status': 'healthy',
            'django_version': django.get_version(),
            'debug': settings.DEBUG,
            'allowed_hosts': settings.ALLOWED_HOSTS,
            'installed_apps_count': len(settings.INSTALLED_APPS),
            'channels_installed': 'channels' in settings.INSTALLED_APPS,
            'asgi_application': getattr(settings, 'ASGI_APPLICATION', None),
        }

        return Response(health_info, status=status.HTTP_200_OK)

    except Exception as e:
        return Response({
            'status': 'unhealthy',
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
def simple_websocket_health(request):
    """
    Simple WebSocket health check that doesn't test Redis connectivity
    """
    try:
        from django.conf import settings

        checks = {}

        # Check if channels is installed
        channels_installed = 'channels' in settings.INSTALLED_APPS
        checks['channels_installed'] = {
            'healthy': channels_installed,
            'message': f"Channels app installed: {channels_installed}"
        }

        # Check ASGI application
        asgi_app = getattr(settings, 'ASGI_APPLICATION', None)
        checks['asgi_config'] = {
            'healthy': bool(asgi_app),
            'message': f"ASGI application: {asgi_app or 'Not configured'}"
        }

        # Check Redis URL configuration
        redis_url = getattr(settings, 'REDIS_CHANNELS_URL', None)
        checks['redis_config'] = {
            'healthy': bool(redis_url),
            'message': f"Redis URL configured: {bool(redis_url)}"
        }

        # Check channel layers configuration
        channel_layers = getattr(settings, 'CHANNEL_LAYERS', {})
        checks['channel_layers'] = {
            'healthy': bool(channel_layers),
            'message': f"Channel layers configured: {bool(channel_layers)}"
        }

        # Try to import channel layer (without testing connection)
        try:
            from channels.layers import get_channel_layer
            channel_layer = get_channel_layer()
            checks['channel_layer_import'] = {
                'healthy': channel_layer is not None,
                'message': f"Channel layer import: {'Success' if channel_layer else 'Failed'}"
            }
        except Exception as e:
            checks['channel_layer_import'] = {
                'healthy': False,
                'message': f"Channel layer import failed: {str(e)}"
            }

        overall_healthy = all(check['healthy'] for check in checks.values())

        result = {
            'healthy': overall_healthy,
            'checks': checks,
            'timestamp': str(timezone.now())
        }

        status_code = status.HTTP_200_OK if overall_healthy else status.HTTP_503_SERVICE_UNAVAILABLE
        return Response(result, status=status_code)

    except Exception as e:
        import traceback
        return Response({
            'healthy': False,
            'error': str(e),
            'traceback': traceback.format_exc()
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)





@api_view(['GET'])
def redis_health_check(request):
    """
    Dedicated Redis health check endpoint
    """
    try:
        from django.conf import settings
        import redis
        import ssl
        import urllib.parse

        redis_url = getattr(settings, 'REDIS_CHANNELS_URL', None)
        if not redis_url:
            return Response({
                'healthy': False,
                'error': 'REDIS_CHANNELS_URL not configured'
            }, status=status.HTTP_503_SERVICE_UNAVAILABLE)

        checks = {}

        # Test direct Redis connection
        try:
            if redis_url.startswith('rediss://'):
                # SSL connection
                parsed = urllib.parse.urlparse(redis_url)
                redis_client = redis.Redis(
                    host=parsed.hostname,
                    port=parsed.port or 6379,
                    password=parsed.password,
                    ssl=True,
                    ssl_cert_reqs=ssl.CERT_NONE,
                    ssl_check_hostname=False,
                    socket_connect_timeout=5,
                    socket_timeout=5
                )
            else:
                # Non-SSL connection
                redis_client = redis.from_url(
                    redis_url,
                    socket_connect_timeout=5,
                    socket_timeout=5
                )

            # Test ping
            redis_client.ping()
            checks['redis_ping'] = {
                'healthy': True,
                'message': 'Redis PING successful'
            }

            # Test set/get
            test_key = 'health_check_test'
            test_value = 'test_value'
            redis_client.set(test_key, test_value, ex=30)
            retrieved = redis_client.get(test_key)

            if retrieved and retrieved.decode() == test_value:
                checks['redis_operations'] = {
                    'healthy': True,
                    'message': 'Redis SET/GET successful'
                }
                redis_client.delete(test_key)
            else:
                checks['redis_operations'] = {
                    'healthy': False,
                    'message': 'Redis SET/GET failed'
                }

        except Exception as redis_error:
            checks['redis_connection'] = {
                'healthy': False,
                'message': f'Redis connection failed: {str(redis_error)}'
            }

        # Test channel layer
        try:
            from channels.layers import get_channel_layer
            channel_layer = get_channel_layer()

            if channel_layer:
                checks['channel_layer'] = {
                    'healthy': True,
                    'message': f'Channel layer: {channel_layer.__class__.__name__}'
                }
            else:
                checks['channel_layer'] = {
                    'healthy': False,
                    'message': 'Channel layer not configured'
                }
        except Exception as channel_error:
            checks['channel_layer'] = {
                'healthy': False,
                'message': f'Channel layer error: {str(channel_error)}'
            }

        overall_healthy = all(check['healthy'] for check in checks.values())

        result = {
            'healthy': overall_healthy,
            'redis_url_type': 'SSL' if redis_url.startswith('rediss://') else 'Non-SSL',
            'checks': checks,
            'timestamp': str(timezone.now())
        }

        status_code = status.HTTP_200_OK if overall_healthy else status.HTTP_503_SERVICE_UNAVAILABLE
        return Response(result, status=status_code)

    except Exception as e:
        import traceback
        return Response({
            'healthy': False,
            'error': str(e),
            'traceback': traceback.format_exc()
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

