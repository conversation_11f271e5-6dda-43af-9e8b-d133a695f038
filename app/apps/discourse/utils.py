import logging
from django.conf import settings
from django.contrib.auth import get_user_model
from django.db import transaction, models

from .models import ChatThread, ChatMessage

logger = logging.getLogger(__name__)
User = get_user_model()


def create_default_chat_thread(user):
    """
    Create a default private 1-on-1 chat thread between a new user and the founder user.
    Sends a welcome message from the founder to the new user.

    Args:
        user: The newly created user instance
    """
    try:
        if not settings.FOUNDER_DEFAULT_USER_ID:
            logger.warning("FOUNDER_DEFAULT_USER_ID not set in "
                           "environment variables")
            return

        if not settings.FOUNDER_DEFAULT_CHAT_MESSAGE:
            logger.warning("FOUNDER_DEFAULT_CHAT_MESSAGE not set in "
                           "environment variables")
            return

        # Get the founder user
        try:
            founder_user = User.objects.get(id=settings.FOUNDER_DEFAULT_USER_ID)
        except User.DoesNotExist:
            error_message = (f"Founder user with ID "
                             f"{settings.FOUNDER_DEFAULT_USER_ID} does "
                             f"not exist")
            logger.error(error_message)
            if settings.SENTRY_ENABLED:
                import sentry_sdk
                sentry_sdk.capture_message(
                    error_message,
                    level='error',
                    extra={
                        'founder_user_id': settings.FOUNDER_DEFAULT_USER_ID,
                        'new_user_id': str(user.id),
                        'new_user_email': user.email,
                        'function': 'create_default_chat_thread'
                    }
                )
            return

        if user.id == founder_user.id:
            logger.info(f"Skipping default chat thread creation for founder "
                        f"user {user.id}")
            return

        existing_thread = ChatThread.objects.filter(
            discourse_type='chats',
            participants=user).filter(
            participants=founder_user).annotate(
            participant_count=models.Count('participants')).filter(
            participant_count=2).first()
        if existing_thread:
            logger.info(f"Chat thread already exists between user {user.id} "
                        f"and founder {founder_user.id}")
            return

        with transaction.atomic():
            chat_thread = ChatThread.objects.create(
                title="",  # Will be auto-generated by serializer
                is_private=True,
                discourse_type='chats'
            )

            chat_thread.participants.add(founder_user, user)
            ChatMessage.objects.create(
                thread=chat_thread,
                author=founder_user,
                body=settings.FOUNDER_DEFAULT_CHAT_MESSAGE
            )

            logger.info(f"Created default chat thread {chat_thread.id} between "
                        f"user {user.id} and founder {founder_user.id}")

    except Exception as e:
        error_message = (f"Error creating default chat thread for user "
                         f"{user.id}: {str(e)}")
        logger.error(error_message)

        # Report to Sentry if enabled
        if settings.SENTRY_ENABLED:
            import sentry_sdk
            sentry_sdk.capture_exception(
                e,
                extra={
                    'founder_user_id': getattr(settings,
                                               'FOUNDER_DEFAULT_USER_ID',
                                               'Not set'),
                    'new_user_id': str(user.id),
                    'new_user_email': user.email,
                    'function': 'create_default_chat_thread'
                }
            )
