import logging
from django.conf import settings
from django.contrib.auth import get_user_model
from django.db import transaction, models

from .models import ChatThread, ChatMessage

logger = logging.getLogger(__name__)
User = get_user_model()


def create_default_chat_thread(user):
    """
    Create a default private 1-on-1 chat thread between a new user and the founder user.
    Sends a welcome message from the founder to the new user.
    
    Args:
        user: The newly created user instance
    """
    try:
        # Get founder user ID from environment variable
        founder_user_id = settings.FOUNDER_DEFAULT_USER_ID
        welcome_message = settings.FOUNDER_DEFAULT_CHAT_MESSAGE
        
        if not founder_user_id:
            logger.warning("FOUNDER_DEFAULT_USER_ID not set in environment variables")
            return
            
        if not welcome_message:
            logger.warning("FOUNDER_DEFAULT_CHAT_MESSAGE not set in environment variables")
            return
        
        # Get the founder user
        try:
            founder_user = User.objects.get(id=founder_user_id)
        except User.DoesNotExist:
            logger.error(f"Founder user with ID {founder_user_id} does not exist")
            return
        
        # Don't create a thread if the user is the founder themselves
        if user.id == founder_user.id:
            logger.info(f"Skipping default chat thread creation for founder user {user.id}")
            return
        
        # Check if a chat thread already exists between these two users
        existing_thread = ChatThread.objects.filter(
            discourse_type='chats',
            participants=user
        ).filter(
            participants=founder_user
        ).annotate(
            participant_count=models.Count('participants')
        ).filter(
            participant_count=2
        ).first()
        
        if existing_thread:
            logger.info(f"Chat thread already exists between user {user.id} and founder {founder_user.id}")
            return
        
        # Create the chat thread and welcome message in a transaction
        with transaction.atomic():
            # Create private chat thread
            chat_thread = ChatThread.objects.create(
                title="",  # Will be auto-generated by serializer
                is_private=True,
                discourse_type='chats'
            )
            
            # Add both users as participants
            chat_thread.participants.add(founder_user, user)
            
            # Create welcome message from founder
            ChatMessage.objects.create(
                thread=chat_thread,
                author=founder_user,
                body=welcome_message
            )
            
            logger.info(f"Created default chat thread {chat_thread.id} between user {user.id} and founder {founder_user.id}")
            
    except Exception as e:
        logger.error(f"Error creating default chat thread for user {user.id}: {str(e)}")
