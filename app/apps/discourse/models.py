import uuid

from django.db import models

from config import settings
from config.storage import DiscoursePublicMediaStorage



class ChatThread(models.Model):
    DISCOURSE_TYPE_CHOICES = [
        ('chats', 'Chat'),
        ('discussions', 'Discussion'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    title = models.CharField(max_length=255, blank=True)
    description = models.CharField(max_length=80, blank=True, null=True)
    is_private = models.BooleanField(default=True)
    discourse_type = models.CharField(
        max_length=20,
        choices=DISCOURSE_TYPE_CHOICES,
        default='chats'
    )
    participants = models.ManyToManyField(
        settings.AUTH_USER_MODEL,
        through='ThreadParticipant',
        related_name='chat_threads'
    )
    pending_request = models.<PERSON><PERSON>an<PERSON>ield(
        default=False,
        help_text='True if this is a pending chat request that needs to be accepted'
    )
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='created_chat_threads',
        null=True,
        blank=True,
        help_text='User who created/initiated this chat thread'
    )
    created = models.DateTimeField(auto_now_add=True)

    class Meta:
        indexes = [
            models.Index(fields=['created']),
        ]

    def __str__(self):
        return f"{self.title or 'Untitled'} ({self.id})"

    def are_participants_connected(self):
        """
        Check if participants in a 1-on-1 chat are mutually following each other.
        Returns True if they are connected (both follow each other), False otherwise.
        Only applicable for private 1-on-1 chats.
        """
        if not self.is_private or self.discourse_type != 'chats':
            return True  # Not applicable for public chats or discussions

        participants = list(self.participants.all())
        if len(participants) != 2:
            return True  # Not a 1-on-1 chat

        user1, user2 = participants
        # Check if both users follow each other
        user1_follows_user2 = user1.profile.is_following(user2)
        user2_follows_user1 = user2.profile.is_following(user1)

        return user1_follows_user2 and user2_follows_user1

    def get_other_participant(self, current_user):
        """
        Get the other participant in a 1-on-1 chat thread.
        Returns None if not a 1-on-1 chat or user is not a participant.
        """
        participants = list(self.participants.all())
        if len(participants) != 2:
            return None

        for participant in participants:
            if participant.id != current_user.id:
                return participant
        return None

    def can_user_send_messages(self, user):
        """
        Check if a user can send messages in this thread.
        Returns False if thread is pending and user is not the receiver.
        """
        if not self.pending_request:
            return True

        # If thread is pending, only the receiver (not the creator) can send messages
        # by replying, which will automatically accept the request
        return user != self.created_by

    def accept_request(self):
        """
        Accept a pending chat request.
        """
        if self.pending_request:
            self.pending_request = False
            self.save()

    def reject_request(self):
        """
        Reject a pending chat request by deleting the thread.
        """
        if self.pending_request:
            self.delete()


class ThreadParticipant(models.Model):
    thread = models.ForeignKey(
        ChatThread,
        on_delete=models.CASCADE,
        related_name='thread_participants'
    )
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='thread_memberships'
    )
    joined_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ('thread', 'user')
        indexes = [
            models.Index(fields=['user']),
            models.Index(fields=['thread', 'joined_at']),
        ]

    def __str__(self):
        return f"{self.user} in {self.thread}"


class ChatMessage(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    thread = models.ForeignKey(
        ChatThread,
        on_delete=models.CASCADE,
        related_name='messages',
        db_index=True
    )
    author = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='chat_messages'
    )
    parent_message = models.ForeignKey(
        'self',
        on_delete=models.CASCADE,
        related_name='replies',
        null=True,
        blank=True,
        db_index=True
    )
    body = models.TextField()
    attachment = models.FileField(
        storage=DiscoursePublicMediaStorage(),
        null=True,
        blank=True
    )
    attachment_url = models.CharField(max_length=500, null=True, blank=True)
    created = models.DateTimeField(auto_now_add=True, db_index=True)

    class Meta:
        ordering = ['created']
        indexes = [
            models.Index(fields=['thread', 'created']),
        ]

    def __str__(self):
        return f"Message from {self.author} in {self.thread}"

    def get_root_message(self):
        """
        Get the root message (top-level message) for this message.
        If this is already a root message, returns self.
        """
        if self.parent_message is None:
            return self
        return self.parent_message

    def is_reply(self):
        """
        Check if this message is a reply to another message.
        """
        return self.parent_message is not None
