import uuid

from django.db import models

from config import settings
from config.storage import DiscoursePublicMediaStorage



class ChatThread(models.Model):
    DISCOURSE_TYPE_CHOICES = [
        ('chats', 'Chat'),
        ('discussions', 'Discussion'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    title = models.CharField(max_length=255, blank=True)
    description = models.CharField(max_length=80, blank=True, null=True)
    is_private = models.BooleanField(default=True)
    discourse_type = models.CharField(
        max_length=20,
        choices=DISCOURSE_TYPE_CHOICES,
        default='chats'
    )
    participants = models.ManyToManyField(
        settings.AUTH_USER_MODEL,
        through='ThreadParticipant',
        related_name='chat_threads'
    )
    created = models.DateTimeField(auto_now_add=True)

    class Meta:
        indexes = [
            models.Index(fields=['created']),
        ]

    def __str__(self):
        return f"{self.title or 'Untitled'} ({self.id})"


class ThreadParticipant(models.Model):
    thread = models.ForeignKey(
        ChatThread,
        on_delete=models.CASCADE,
        related_name='thread_participants'
    )
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='thread_memberships'
    )
    joined_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ('thread', 'user')
        indexes = [
            models.Index(fields=['user']),
            models.Index(fields=['thread', 'joined_at']),
        ]

    def __str__(self):
        return f"{self.user} in {self.thread}"


class ChatMessage(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    thread = models.ForeignKey(
        ChatThread,
        on_delete=models.CASCADE,
        related_name='messages',
        db_index=True
    )
    author = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='chat_messages'
    )
    parent_message = models.ForeignKey(
        'self',
        on_delete=models.CASCADE,
        related_name='replies',
        null=True,
        blank=True,
        db_index=True
    )
    body = models.TextField()
    attachment = models.FileField(
        storage=DiscoursePublicMediaStorage(),
        null=True,
        blank=True
    )
    attachment_url = models.CharField(max_length=500, null=True, blank=True)
    created = models.DateTimeField(auto_now_add=True, db_index=True)

    class Meta:
        ordering = ['created']
        indexes = [
            models.Index(fields=['thread', 'created']),
        ]

    def __str__(self):
        return f"Message from {self.author} in {self.thread}"

    def get_root_message(self):
        """
        Get the root message (top-level message) for this message.
        If this is already a root message, returns self.
        """
        if self.parent_message is None:
            return self
        return self.parent_message

    def is_reply(self):
        """
        Check if this message is a reply to another message.
        """
        return self.parent_message is not None
