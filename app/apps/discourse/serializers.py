from rest_framework import serializers
from django.contrib.auth import get_user_model

from apps.discourse.cache import ChatCacheManager
from apps.discourse.models import (
    ChatMessage,
    ChatThread,
    ThreadParticipant,
)



User = get_user_model()



class UserChatSerializer(serializers.ModelSerializer):
    picture = serializers.SerializerMethodField()
    nickname = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = ['id',
                  'email',
                  'first_name',
                  'last_name',
                  'nickname',
                  'picture']

    def get_picture(self, obj):
        """
        Get the tiny profile photo from the user's profile public_details.
        """
        try:
            # Access the profile through the user relationship
            if hasattr(obj, 'profile') and obj.profile:
                return obj.profile.private_details.get('profile_photo_tiny', '')
            return ''
        except Exception:
            # Return empty string if any error occurs
            return ''

    def get_nickname(self, obj):
        try:
            # Access the profile through the user relationship
            if hasattr(obj, 'profile') and obj.profile:
                return obj.profile.private_details.get('nickname', '')
            return ''
        except Exception:
            # Return empty string if any error occurs
            return ''


class ChatMessageSerializer(serializers.ModelSerializer):
    author_details = UserChatSerializer(source='author', read_only=True)
    replies = serializers.SerializerMethodField()
    reply_count = serializers.SerializerMethodField()

    class Meta:
        model = ChatMessage
        fields = ['id',
                  'thread',
                  'author',
                  'author_details',
                  'parent_message',
                  'body',
                  'attachment',
                  'attachment_url',
                  'replies',
                  'reply_count',
                  'created']
        read_only_fields = ['author', 'thread', 'created']

    def get_replies(self, obj):
        """
        Get replies for this message.
        Replies are sorted by creation date.
        """
        replies = obj.replies.all().order_by('created')
        # Use a simplified serializer to avoid infinite recursion
        return ChatMessageReplySerializer(replies, many=True, context=self.context).data

    def get_reply_count(self, obj):
        """
        Get the count of replies for this message.
        """
        return obj.replies.count()

    def create(self, validated_data):
        # Set the author to the current user
        current_user = self.context['request'].user
        validated_data['author'] = current_user

        # Get the thread from validated_data
        thread = validated_data.get('thread')
        if thread:
            # Check if user can send messages in this thread
            if not thread.can_user_send_messages(current_user):
                from rest_framework.exceptions import PermissionDenied
                raise PermissionDenied("Cannot send messages in a pending chat request. Wait for the other user to accept.")

            # If this is a reply to a pending thread by the receiver, accept the request
            if thread.pending_request and current_user != thread.created_by:
                thread.accept_request()
                import logging
                logger = logging.getLogger(__name__)
                logger.info(f"Chat request accepted by user {current_user.id} in thread {thread.id}")

        # Handle reply logic - ensure only 1 level of nesting
        parent_message = validated_data.get('parent_message')
        if parent_message and parent_message.parent_message is not None:
            # If trying to reply to a reply, reply to the root message instead
            validated_data['parent_message'] = parent_message.parent_message

        return super().create(validated_data)


class ChatMessageReplySerializer(serializers.ModelSerializer):
    """
    Serializer for replies that includes nested replies.
    """
    author_details = UserChatSerializer(source='author', read_only=True)
    replies = serializers.SerializerMethodField()
    reply_count = serializers.SerializerMethodField()

    class Meta:
        model = ChatMessage
        fields = ['id',
                  'thread',
                  'author',
                  'author_details',
                  'parent_message',
                  'body',
                  'attachment',
                  'attachment_url',
                  'replies',
                  'reply_count',
                  'created']
        read_only_fields = ['author', 'thread', 'created']

    def get_replies(self, obj):
        """
        Get replies for this message.
        Replies are sorted by creation date.
        """
        replies = obj.replies.all().order_by('created')
        # Use the same serializer for consistency (replies can have replies)
        return ChatMessageReplySerializer(replies, many=True, context=self.context).data

    def get_reply_count(self, obj):
        """
        Get the count of replies for this message.
        """
        return obj.replies.count()


class ThreadParticipantSerializer(serializers.ModelSerializer):
    user_details = UserChatSerializer(source='user', read_only=True)

    class Meta:
        model = ThreadParticipant
        fields = ['id',
                  'user',
                  'user_details',
                  'joined_at']


class ChatThreadSerializer(serializers.ModelSerializer):
    participants = serializers.PrimaryKeyRelatedField(
        many=True,
        queryset=User.objects.all(),
        required=False
    )
    participants_details = UserChatSerializer(source='participants', many=True, read_only=True)
    last_message = serializers.SerializerMethodField()
    recent_messages = serializers.SerializerMethodField()
    is_participant = serializers.SerializerMethodField()
    messages_count = serializers.SerializerMethodField()
    can_send_messages = serializers.SerializerMethodField()
    other_participant = serializers.SerializerMethodField()

    class Meta:
        model = ChatThread
        fields = ['id',
                  'title',
                  'description',
                  'is_private',
                  'discourse_type',
                  'participants',
                  'participants_details',
                  'last_message',
                  'recent_messages',
                  'is_participant',
                  'messages_count',
                  'pending_request',
                  'created_by',
                  'can_send_messages',
                  'other_participant',
                  'created']

    def get_last_message(self, obj):
        last_message = obj.messages.order_by('-created').first()
        if last_message:
            return ChatMessageSerializer(last_message).data
        return None

    def get_recent_messages(self, obj):
        """
        Get recent messages for this thread.
        """
        request = self.context.get('request')
        if not request:
            return None

        try:
            cached_messages = ChatCacheManager.get_thread_messages(obj.id)
            if cached_messages is not None:
                return cached_messages
        except Exception as cache_error:
            import logging
            logger = logging.getLogger(__name__)
            logger.warning(f"Cache error in get_recent_messages: {cache_error}")
            # Continue without cache

        # Fallback: get messages directly from database
        recent_messages = obj.messages.order_by('-created')[:30]
        if recent_messages:
            # Try to cache, but don't fail if caching fails
            try:
                return ChatCacheManager.cache_thread_messages(obj.id, recent_messages)
            except Exception as cache_error:
                import logging
                logger = logging.getLogger(__name__)
                logger.warning(f"Cache error when storing recent messages: {cache_error}")
                # Return serialized messages directly without caching
                serialized_messages = []
                for message in recent_messages:
                    serialized_messages.append({
                        'id': str(message.id),
                        'thread': str(message.thread.id),
                        'author': message.author.id,
                        'author_details': {
                            'id': message.author.id,
                            'email': message.author.email,
                            'first_name': message.author.first_name,
                            'last_name': message.author.last_name,
                            'nickname': message.author.profile.private_details.get('nickname', ''),
                            'picture': (
                                message.author.profile.public_details.get('profile_photo_tiny', '')
                                if hasattr(message.author, 'profile') and message.author.profile
                                else ''
                            )
                        },
                        'body': message.body,
                        'attachment': message.attachment.url if message.attachment else None,
                        'attachment_url': message.attachment.url if message.attachment else None,
                        'created': message.created.isoformat()
                    })
                return serialized_messages
        return []

    def get_is_participant(self, obj):
        """
        Check if the current user is a participant in this thread.
        """
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            return obj.participants.filter(id=request.user.id).exists()
        return False

    def get_messages_count(self, obj):
        """
        Get the total count of messages in this thread.
        Uses annotated value if available, otherwise counts from database.
        """
        # Check if the count was annotated in the queryset (more efficient)
        if hasattr(obj, 'messages_count_annotated'):
            return obj.messages_count_annotated

        # Fallback to counting messages directly
        return obj.messages.count()

    def get_can_send_messages(self, obj):
        """
        Check if the current user can send messages in this thread.
        Returns False if thread is pending and user is the creator.
        """
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            return obj.can_user_send_messages(request.user)
        return False

    def get_other_participant(self, obj):
        """
        Get the other participant in a 1-on-1 chat thread.
        Returns user details for the other participant.
        """
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            other_user = obj.get_other_participant(request.user)
            if other_user:
                return {
                    'id': other_user.id,
                    'email': other_user.email,
                    'first_name': other_user.first_name,
                    'last_name': other_user.last_name,
                    'full_name': other_user.full_name
                }
        return None

    def to_representation(self, instance):
        """
        Customize the representation to hide auto-generated titles from frontend.
        Only private chat auto-generated titles are hidden.
        For discussions, all auto-generated titles are visible.
        """
        data = super().to_representation(instance)

        # Hide auto-generated titles for private chats from frontend
        # Discussion titles (both private and public) should be visible
        if instance.discourse_type == 'chats':
            # Remove the title from the response or set it to empty
            data['title'] = ''

        return data

    def create(self, validated_data):
        import logging
        logger = logging.getLogger(__name__)

        try:
            participants = validated_data.pop('participants', [])
            logger.info(f"Creating thread with participants: {[str(p.id) for p in participants]}")

            # Set discourse_type based on the request context (view basename)
            view = self.context.get('view')
            if view and hasattr(view, 'basename'):
                basename = view.basename
                if 'discussion' in basename:
                    validated_data['discourse_type'] = 'discussions'
                else:
                    validated_data['discourse_type'] = 'chats'
            else:
                # Fallback: check if discourse_type is explicitly provided
                if 'discourse_type' not in validated_data:
                    validated_data['discourse_type'] = 'chats'

            # Handle participants based on discourse type
            current_user = self.context['request'].user
            discourse_type = validated_data.get('discourse_type', 'chats')

            # Set created_by to current user
            validated_data['created_by'] = current_user

            if discourse_type == 'discussions':
                # For discussions, a thread can be created by a user with only 1 participant (themselves)
                if not participants:
                    participants = [current_user]
                elif current_user not in participants:
                    participants.append(current_user)
            else:
                # For chats, always include the current user as a participant
                if current_user not in participants:
                    participants.append(current_user)
                    logger.info(f"Added current user {current_user.id} to participants")

                # Check if this is a 1-on-1 private chat and if users are connected
                is_private = validated_data.get('is_private', True)
                if is_private and len(participants) == 2:
                    other_user = None
                    for participant in participants:
                        if participant.id != current_user.id:
                            other_user = participant
                            break

                    if other_user:
                        # Check if users are mutually following each other
                        current_follows_other = current_user.profile.is_following(other_user)
                        other_follows_current = other_user.profile.is_following(current_user)

                        # If they're not mutually connected, set as pending request
                        if not (current_follows_other and other_follows_current):
                            validated_data['pending_request'] = True
                            logger.info(f"Setting chat thread as pending request between {current_user.id} and {other_user.id}")

            # Generate default title if no title provided
            is_private = validated_data.get('is_private', False)
            title = validated_data.get('title', '')
            discourse_type = validated_data.get('discourse_type', 'chats')

            if not title:
                # Create thread first to get the UUID (needed for both private and public)
                thread = ChatThread.objects.create(**validated_data)

                if discourse_type == 'discussions':
                    # This shouldn't happen due to view validation, but just in case
                    if is_private:
                        thread_uuid_short = str(thread.id)[:8]
                        thread.title = f"Discussion: {thread_uuid_short}"
                        logger.info(f"Generated fallback title for private discussion {thread.id}: {thread.title}")
                    else:
                        creator_name = f"{current_user.first_name} {current_user.last_name}".strip()
                        thread.title = f"{creator_name}'s Discussion"
                        logger.info(f"Generated fallback title for public discussion {thread.id}: {thread.title}")
                elif len(participants) == 2:
                    # Generate title using first 8 characters of UUID for chats
                    thread_uuid_short = str(thread.id)[:8]
                    thread.title = f"Chat [{thread_uuid_short}]"
                    logger.info(f"Generated default title for chat thread {thread.id}: {thread.title}")
                else:
                    # Generate title using participants' names for public chats
                    participant_names = []
                    for user in participants:
                        full_name = f"{user.first_name} {user.last_name}".strip()
                        if full_name:
                            participant_names.append(full_name)
                        else:
                            # Fallback to email if no name available
                            participant_names.append(user.email)

                    if len(participant_names) == 1:
                        thread.title = participant_names[0]
                    elif len(participant_names) == 2:
                        thread.title = f"{participant_names[0]} and {participant_names[1]}"
                    elif len(participant_names) > 2:
                        # Join all but last with commas, then add "and" before the last
                        thread.title = f"{', '.join(participant_names[:-1])} and {participant_names[-1]}"
                    else:
                        # Fallback if no participants (shouldn't happen)
                        thread.title = "Chat Thread"

                    logger.info(f"Generated default title for public thread {thread.id}: {thread.title}")

                thread.save()
            else:
                # Create thread with provided title
                thread = ChatThread.objects.create(**validated_data)
                logger.info(f"Created thread {thread.id} with title: {thread.title}")

            for user in participants:
                ThreadParticipant.objects.create(thread=thread, user=user)
                logger.info(f"Added participant {user.id} to thread {thread.id}")

            return thread
        except Exception as e:
            logger.error(f"Error creating chat thread: {str(e)}")
            raise

    def update(self, instance, validated_data):
        participants = validated_data.pop('participants', None)

        # Update thread fields
        instance = super().update(instance, validated_data)

        # Update participants if provided
        if participants is not None:
            # Ensure current user remains a participant
            if self.context['request'].user not in participants:
                participants.append(self.context['request'].user)

            # Get current participants
            current_participants = set(instance.participants.all())
            new_participants = set(participants)

            # Remove participants not in the new list
            to_remove = current_participants - new_participants
            for user in to_remove:
                ThreadParticipant.objects.filter(thread=instance, user=user).delete()

            # Add new participants
            to_add = new_participants - current_participants
            for user in to_add:
                ThreadParticipant.objects.create(thread=instance, user=user)

        return instance
