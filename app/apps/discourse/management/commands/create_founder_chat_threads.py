from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.conf import settings

from apps.discourse.utils import create_default_chat_thread

User = get_user_model()


class Command(BaseCommand):
    help = 'Create default chat threads with founder user for all existing users who don\'t have one'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be created without actually creating anything',
        )
        parser.add_argument(
            '--user-id',
            type=str,
            help='Create chat thread for a specific user ID only',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        specific_user_id = options.get('user_id')

        # Check if environment variables are set
        founder_user_id = settings.FOUNDER_DEFAULT_USER_ID
        welcome_message = settings.FOUNDER_DEFAULT_CHAT_MESSAGE

        if not founder_user_id:
            self.stdout.write(
                self.style.ERROR('FOUNDER_DEFAULT_USER_ID not set in environment variables')
            )
            return

        if not welcome_message:
            self.stdout.write(
                self.style.ERROR('FOUNDER_DEFAULT_CHAT_MESSAGE not set in environment variables')
            )
            return

        # Check if founder user exists
        try:
            founder_user = User.objects.get(id=founder_user_id)
            self.stdout.write(
                self.style.SUCCESS(f'Found founder user: {founder_user.email} ({founder_user.id})')
            )
        except User.DoesNotExist:
            error_message = f'Founder user with ID {founder_user_id} does not exist'
            self.stdout.write(self.style.ERROR(error_message))

            # Report to Sentry if enabled
            if settings.SENTRY_ENABLED:
                import sentry_sdk
                sentry_sdk.capture_message(
                    error_message,
                    level='error',
                    extra={
                        'founder_user_id': founder_user_id,
                        'command': 'create_founder_chat_threads'
                    }
                )
            return

        # Get users to process
        if specific_user_id:
            try:
                users = [User.objects.get(id=specific_user_id)]
                self.stdout.write(f'Processing specific user: {users[0].email}')
            except User.DoesNotExist:
                self.stdout.write(
                    self.style.ERROR(f'User with ID {specific_user_id} does not exist')
                )
                return
        else:
            # Get all users except the founder
            users = User.objects.exclude(id=founder_user_id)
            self.stdout.write(f'Processing {users.count()} users (excluding founder)')

        created_count = 0
        skipped_count = 0
        error_count = 0

        for user in users:
            try:
                if dry_run:
                    # Check if thread would be created
                    from apps.discourse.models import ChatThread
                    from django.db import models

                    existing_thread = ChatThread.objects.filter(
                        discourse_type='chats',
                        participants=user
                    ).filter(
                        participants=founder_user
                    ).annotate(
                        participant_count=models.Count('participants')
                    ).filter(
                        participant_count=2
                    ).first()

                    if existing_thread:
                        self.stdout.write(f'  SKIP: {user.email} - thread already exists')
                        skipped_count += 1
                    else:
                        self.stdout.write(f'  CREATE: {user.email} - would create new thread')
                        created_count += 1
                else:
                    # Actually create the thread
                    initial_thread_count = user.chat_threads.filter(
                        discourse_type='chats',
                        participants=founder_user
                    ).count()

                    create_default_chat_thread(user)

                    # Check if thread was created
                    final_thread_count = user.chat_threads.filter(
                        discourse_type='chats',
                        participants=founder_user
                    ).count()

                    if final_thread_count > initial_thread_count:
                        self.stdout.write(f'  ✓ Created thread for: {user.email}')
                        created_count += 1
                    else:
                        self.stdout.write(f'  - Skipped (already exists): {user.email}')
                        skipped_count += 1

            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'  ✗ Error processing {user.email}: {str(e)}')
                )
                error_count += 1

        # Summary
        self.stdout.write('\n' + '='*50)
        if dry_run:
            self.stdout.write(self.style.SUCCESS('DRY RUN SUMMARY:'))
            self.stdout.write(f'Would create: {created_count} threads')
        else:
            self.stdout.write(self.style.SUCCESS('EXECUTION SUMMARY:'))
            self.stdout.write(f'Created: {created_count} threads')

        self.stdout.write(f'Skipped: {skipped_count} users (already have threads)')
        if error_count > 0:
            self.stdout.write(self.style.ERROR(f'Errors: {error_count} users'))

        if dry_run:
            self.stdout.write('\nRun without --dry-run to actually create the threads.')