from django.core.cache import cache

from config import settings



class ChatCacheManager:
    """
    Manages caching for chat messages using Redis.
    """

    @staticmethod
    def get_thread_messages_key(thread_id):
        """Get the Redis key for a thread's messages."""
        return f'thread:{thread_id}:messages'

    @staticmethod
    def get_thread_list_key(user_id, discourse_type, exclude_public=False):
        """Get the Redis key for a user's thread list."""
        public_suffix = '_exclude_public' if exclude_public else '_with_public'
        return f'user:{user_id}:threads:{discourse_type}{public_suffix}'

    @staticmethod
    def cache_message(message):
        """
        Cache a new message in the thread's message list.
        Also invalidates thread list caches since message count changed.
        """
        try:
            thread_id = str(message.thread.id)
            cache_key = ChatCacheManager.get_thread_messages_key(thread_id)

            cached_messages = cache.get(cache_key) or []

            from apps.discourse.serializers import ChatMessageSerializer
            serialized = ChatMessageSerializer(message).data
            cached_messages.append(serialized)

            # Keep only the last 50 messages
            if len(cached_messages) > 50:
                cached_messages = cached_messages[-50:]

            # Update cache with timeout from settings
            cache.set(cache_key, cached_messages, timeout=settings.MINIMUM_CACHE_TTL)

            # Invalidate thread list caches for all participants since message count changed
            ChatCacheManager.invalidate_thread_participants_cache(message.thread)

            return serialized
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.warning(f"Cache error in cache_message: {e}")
            # Return serialized message even if caching failed
            try:
                from apps.discourse.serializers import ChatMessageSerializer
                return ChatMessageSerializer(message).data
            except Exception as serializer_error:
                logger.error(f"Serialization error in cache_message fallback: {serializer_error}")
                # Return basic message data as fallback
                return {
                    'id': str(message.id),
                    'thread': str(message.thread.id),
                    'author': message.author.id,
                    'author_details': {
                        'id': message.author.id,
                        'email': message.author.email,
                        'first_name': message.author.first_name,
                        'last_name': message.author.last_name,
                        'picture': (
                            message.author.profile.public_details.get('profile_photo_tiny', '')
                            if hasattr(message.author, 'profile') and message.author.profile
                            else ''
                        )
                    },
                    'body': message.body,
                    'created': message.created.isoformat()
                }

    @staticmethod
    def get_thread_messages(thread_id, limit=30):
        """
        Get cached messages for a thread.
        Returns None if not in cache or if cache fails.

        Args:
            thread_id: The thread ID
            limit: Maximum number of messages to return

        Returns:
            List of serialized messages or None if not cached/cache fails
        """
        try:
            cache_key = ChatCacheManager.get_thread_messages_key(thread_id)
            cached_messages = cache.get(cache_key)

            if cached_messages is not None:
                # Return only the requested number of messages
                # Messages are stored newest first, so we take the first 'limit' messages
                return cached_messages[:limit]

            return cached_messages
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.warning(f"Cache get error in get_thread_messages: {e}")
            return None

    @staticmethod
    def cache_thread_messages(thread_id, messages):
        """
        Cache a list of messages for a thread.

        Args:
            thread_id: The thread ID
            messages: List of message objects or serialized messages

        Returns:
            Serialized messages
        """
        try:
            cache_key = ChatCacheManager.get_thread_messages_key(thread_id)

            # Serialize messages if they're not already serialized
            if messages and not isinstance(messages[0], dict):
                from apps.discourse.serializers import ChatMessageSerializer
                serialized = ChatMessageSerializer(messages, many=True).data
            else:
                serialized = messages

            # We store up to 50 messages in cache (more than the default page size)
            # This allows for some flexibility in page size without cache misses
            if len(serialized) > 50:
                serialized = serialized[:50]

            cache.set(cache_key, serialized, timeout=settings.MINIMUM_CACHE_TTL)

            return serialized
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.warning(f"Cache set error in cache_thread_messages: {e}")
            # Return the serialized messages even if caching failed
            if messages and not isinstance(messages[0], dict):
                from apps.discourse.serializers import ChatMessageSerializer
                return ChatMessageSerializer(messages, many=True).data
            else:
                return messages

    @staticmethod
    def cache_thread_list(user_id, discourse_type, threads_data, exclude_public=False):
        """
        Cache a user's thread list.

        Args:
            user_id: The user ID
            discourse_type: 'chats' or 'discussions'
            threads_data: Serialized thread data
            exclude_public: Whether this list excludes public threads (default: False, includes public)
        """
        try:
            cache_key = ChatCacheManager.get_thread_list_key(user_id, discourse_type, exclude_public)
            cache.set(cache_key, threads_data, timeout=settings.MINIMUM_CACHE_TTL)
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.warning(f"Cache set error in cache_thread_list: {e}")

    @staticmethod
    def get_thread_list(user_id, discourse_type, exclude_public=False):
        """
        Get cached thread list for a user.

        Args:
            user_id: The user ID
            discourse_type: 'chats' or 'discussions'
            exclude_public: Whether this list excludes public threads (default: False, includes public)

        Returns:
            Cached thread list or None if not cached
        """
        try:
            cache_key = ChatCacheManager.get_thread_list_key(user_id, discourse_type, exclude_public)
            return cache.get(cache_key)
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.warning(f"Cache get error in get_thread_list: {e}")
            return None

    @staticmethod
    def invalidate_thread_cache(thread_id):
        """
        Remove a thread's messages from cache.
        """
        try:
            cache_key = ChatCacheManager.get_thread_messages_key(thread_id)
            cache.delete(cache_key)
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.warning(f"Cache delete error in invalidate_thread_cache: {e}")

    @staticmethod
    def invalidate_user_thread_lists(user_id):
        """
        Invalidate all thread list caches for a user.
        This should be called when a thread's message count changes.
        """
        try:
            # Invalidate all possible thread list combinations for this user
            # Both exclude_public=False (includes public) and exclude_public=True (excludes public)
            cache_keys = [
                ChatCacheManager.get_thread_list_key(user_id, 'chats', False),  # includes public
                ChatCacheManager.get_thread_list_key(user_id, 'chats', True),   # excludes public
                ChatCacheManager.get_thread_list_key(user_id, 'discussions', False),  # includes public
                ChatCacheManager.get_thread_list_key(user_id, 'discussions', True),   # excludes public
            ]
            cache.delete_many(cache_keys)
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.warning(f"Cache delete error in invalidate_user_thread_lists: {e}")

    @staticmethod
    def invalidate_thread_participants_cache(thread):
        """
        Invalidate thread list caches for all participants of a thread.
        This should be called when a message is added to ensure message counts are updated.
        """
        try:
            # Get all participant IDs for this thread
            participant_ids = list(thread.participants.values_list('id', flat=True))

            # Invalidate thread lists for all participants
            for user_id in participant_ids:
                ChatCacheManager.invalidate_user_thread_lists(user_id)

        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.warning(f"Cache delete error in invalidate_thread_participants_cache: {e}")
