from django.contrib import admin

from .models import (
    ChatMessage,
    ChatThread,
    ThreadParticipant,
)



class ThreadParticipantInline(admin.TabularInline):
    model = ThreadParticipant
    extra = 1

class ChatMessageInline(admin.TabularInline):
    model = ChatMessage
    extra = 0
    readonly_fields = ('created',)
    fields = ('author', 'body', 'attachment', 'created')

@admin.register(ChatThread)
class ChatThreadAdmin(admin.ModelAdmin):
    list_display = ('id', 'title', 'is_private', 'pending_request', 'created_by', 'created', 'participant_count', 'message_count')
    list_filter = ('is_private', 'pending_request', 'created', 'discourse_type')
    search_fields = ('title', 'participants__username', 'participants__email', 'created_by__email')
    readonly_fields = ('created',)
    inlines = [ThreadParticipantInline, ChatMessageInline]

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('created_by')

    def participant_count(self, obj):
        return obj.participants.count()

    def message_count(self, obj):
        return obj.messages.count()

@admin.register(ChatMessage)
class ChatMessageAdmin(admin.ModelAdmin):
    list_display = ('id', 'thread', 'author', 'short_body', 'created')
    list_filter = ('created', 'author')
    search_fields = ('body', 'author__username', 'thread__title')
    readonly_fields = ('created',)

    def short_body(self, obj):
        return obj.body[:50] + '...' if len(obj.body) > 50 else obj.body
