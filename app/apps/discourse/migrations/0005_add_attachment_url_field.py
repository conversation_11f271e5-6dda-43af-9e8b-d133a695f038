# Generated by Django 5.1.4 on 2025-06-04 13:36

import config.storage
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('discourse', '0004_chatmessage_parent_message'),
    ]

    operations = [
        migrations.AddField(
            model_name='chatmessage',
            name='attachment_url',
            field=models.CharField(blank=True, max_length=500, null=True),
        ),
        migrations.AlterField(
            model_name='chatmessage',
            name='attachment',
            field=models.FileField(blank=True, null=True, storage=config.storage.DiscoursePublicMediaStorage(), upload_to=''),
        ),
    ]
