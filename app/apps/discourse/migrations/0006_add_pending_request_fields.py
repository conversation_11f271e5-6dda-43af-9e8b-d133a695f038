# Generated by Django 5.1.4 on 2025-06-12 14:46

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('discourse', '0005_add_attachment_url_field'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='chatthread',
            name='created_by',
            field=models.ForeignKey(blank=True, help_text='User who created/initiated this chat thread', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='created_chat_threads', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='chatthread',
            name='pending_request',
            field=models.BooleanField(default=False, help_text='True if this is a pending chat request that needs to be accepted'),
        ),
    ]
