from rest_framework import permissions


class IsThreadParticipant(permissions.BasePermission):
    """
    Permission to only allow participants of a thread to access it.
    For discussions: checks content permissions and auto-enrolls eligible users.
    """

    def has_object_permission(self, request, view, obj):
        # For discussions: check content permissions and auto-enroll if eligible
        if obj.discourse_type == 'discussions':
            # Allow access to public discussions
            if not obj.is_private:
                return True

            # Check if user is already a participant
            if request.user in obj.participants.all():
                return True

            # Check content permissions and auto-enroll if eligible
            from apps.authentication.permissions import auto_enroll_user_in_discussion
            return auto_enroll_user_in_discussion(request.user, str(obj.id))

        # For chats: only participants can access
        return request.user in obj.participants.all()


class IsMessageThreadParticipant(permissions.BasePermission):
    """
    Permission to only allow participants of a thread to access its messages.
    For discussions: checks content permissions and auto-enrolls eligible users.
    """

    def has_permission(self, request, view):
        # For list view, check if thread_id is provided and user has access
        thread_id = request.query_params.get('thread') or view.kwargs.get('thread_id')
        if thread_id:
            from apps.discourse.models import ChatThread
            try:
                thread = ChatThread.objects.get(id=thread_id)
                if thread.discourse_type == 'discussions':
                    # Allow access to public discussions
                    if not thread.is_private:
                        return True

                    # Check if user is already a participant
                    if request.user in thread.participants.all():
                        return True

                    # Check content permissions and auto-enroll if eligible
                    from apps.authentication.permissions import auto_enroll_user_in_discussion
                    return auto_enroll_user_in_discussion(request.user, str(thread_id))
                else:
                    # For chats: only participants can access
                    return request.user.chat_threads.filter(id=thread_id).exists()
            except ChatThread.DoesNotExist:
                return False
        return True  # Will be checked in has_object_permission

    def has_object_permission(self, request, view, obj):
        # Check if the user has access to the message's thread
        thread = obj.thread
        if thread.discourse_type == 'discussions':
            # Allow access to public discussions
            if not thread.is_private:
                return True

            # Check if user is already a participant
            if request.user in thread.participants.all():
                return True

            # Check content permissions and auto-enroll if eligible
            from apps.authentication.permissions import auto_enroll_user_in_discussion
            return auto_enroll_user_in_discussion(request.user, str(thread.id))
        else:
            # For chats: only participants can access
            return request.user in thread.participants.all()
