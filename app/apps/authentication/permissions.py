from rest_framework import permissions

# from django.core.exceptions import ObjectDoesNotExist

from config import settings

# from utils.meilisearch.router import (
#     content_type_index_mapper as index_mapper
# )


class IsStaffOrOwnAccount(permissions.BasePermission):
    message = 'You have no permissions to perform that action.'

    def has_permission(self, request, view):
        if request.auth or request.user:
            user = request.user
        else:
            return False

        try:
            if user.is_authenticated:
                if view.kwargs.get('pk') != 'current':
                    return user.is_staff or str(user.pk) == view.kwargs.get('pk')
                else:
                    return True
        except AttributeError:
            return False
        return False


class IsStaff(permissions.BasePermission):
    message = 'You have no permissions to perform that action.'

    def has_permission(self, request, view):
        try:
            return request.user.is_staff
        except Exception:
            return False


# class IsFreePlanScope(permissions.BasePermission):
#     message = 'You have no permissions to perform that action.'
#     def has_permission(self, request, view):
#         # Read permissions are allowed to any request,
#         # so we'll always allow GET, HEAD or OPTIONS requests.
#         content_type = view.kwargs.get('content_type')
#         slug = view.kwargs.get('pk')
#         view.get_view_name().lower()
#         print(view.get_object())
#         product = index_mapper['courses'].get(slug)
#
#         return product.get('price') == 0
#
#
# class IsThrivePlanScope(permissions.BasePermission):
#     message = 'You have no permissions to perform that action.'
#     def has_permission(self, request, view):
#         # Read permissions are allowed to any request,
#         # so we'll always allow GET, HEAD or OPTIONS requests.
#         if request.auth or request.user:
#             user = request.user
#         else:
#             return False
#
#         try:
#             if request.user.plan_id == settings.CHARGEBEE_THRIVE_PLAN_ID:
#                 return True
#         except AttributeError:
#             return False
#         return False
#
#
# class IsCommunityPlanScope(permissions.BasePermission):
#     message = 'You have no permissions to perform that action.'
#     def has_permission(self, request, view):
#         # Read permissions are allowed to any request,
#         # so we'll always allow GET, HEAD or OPTIONS requests.
#         if request.auth or request.user:
#             user = request.user
#         else:
#             return False
#
#         try:
#             allowed_content_types = ['films', 'podcasts', 'articles']
#             content_type = view.kwargs.get('content_type')
#             if (user.plan_id == settings.CHARGEBEE_COMMUNITY_PLAN_ID and
#                     content_type in allowed_content_types):
#                 return True
#         except AttributeError:
#             return False
#         return False
#
#
# class IsSinglePurchase(permissions.BasePermission):
#     message = 'You have no permissions to perform that action.'
#     def has_permission(self, request, view):
#         if request.auth or request.user:
#             user = request.user
#         else:
#             return False
#
#         try:
#             # if user has purchased the product outside of plan scope
#             product = user.content.get(pk=view.kwargs.get('pk'))
#
#             return product.on_demand
#         except ObjectDoesNotExist:
#             return False
#         return False


class ReadOnly(permissions.BasePermission):
    def has_permission(self, request, view):
        return request.method in permissions.SAFE_METHODS


class IsCMSRequest(permissions.BasePermission):
    def has_permission(self, request, view):
        return settings.CMS_URL in view.request.META['HTTP_REFERER']


class IsChargebeeRequest(permissions.BasePermission):
    def has_permission(self, request, view):
        magic_key = request.query_params.get('magicKey')
        return settings.CHARGEBEE_MAGIC_KEY == magic_key


class IsDigitalOceanFunctionsRequest(permissions.BasePermission):
    def has_permission(self, request, view):
        # get "magicKey" param from form POST
        magic_key = request.data.get('magicKey')
        return settings.DIGITAL_OCEAN_FUNCTIONS_AUTH_TOKEN == magic_key


class IsCMSRequest(permissions.BasePermission):
    def has_permission(self, request, view):
        magic_key = request.query_params.get('magicKey')
        return settings.CMS_MAGIC_KEY == magic_key


def check_product_permissions(user, content_type, instance, plan_ids=None):
    """
    Permission check for Course or Event object.

    Criteria:
    - user is staff
    - the course/event is free
    - the user has an active CHARGEBEE_THRIVE_PLAN_ID membership subscription
    - the user has purchased the product individually in an on-demand order
    - the product is active (meaning the purchase was completed successfully)

    :param user: current user or any given user
    :param content_type: 'courses' or 'events'
    :param instance: the query response from Meilisearch API
    :param plan_ids: user plan_id to check or CHARGEBEE_THRIVE_PLAN_ID ('thrive')
    :return: boolean indicating whether the user is allowed to view the object
    """
    if plan_ids is None:
        plan_ids = (settings.CHARGEBEE_THRIVE_MONTHLY_PLAN_IDS +
                    settings.CHARGEBEE_THRIVE_YEARLY_PLAN_IDS)
        if content_type == 'events':
            plan_ids += (settings.CHARGEBEE_COMMUNITY_MONTHLY_PLAN_IDS +
                         settings.CHARGEBEE_COMMUNITY_YEARLY_PLAN_IDS)

    if ((instance.get('price') == 0 and content_type != 'events') or
            (user.is_authenticated and (
                    user.is_staff or
                    user.plan_id in plan_ids or
                    user.content.filter(
                        content_type=content_type,
                        on_demand=True,
                        slug=instance.get('slug')).exists()))):
        return True
    return False


def check_course_permissions(user, course, content_type='courses'):
    course_plan_ids = (settings.CHARGEBEE_THRIVE_MONTHLY_PLAN_IDS +
                       settings.CHARGEBEE_THRIVE_YEARLY_PLAN_IDS)

    return check_product_permissions(
        user, content_type, course, course_plan_ids)


def check_event_permissions(user, event, content_type='events'):
    event_plan_ids = (settings.CHARGEBEE_THRIVE_MONTHLY_PLAN_IDS +
                      settings.CHARGEBEE_THRIVE_YEARLY_PLAN_IDS +
                      settings.CHARGEBEE_COMMUNITY_MONTHLY_PLAN_IDS +
                      settings.CHARGEBEE_COMMUNITY_YEARLY_PLAN_IDS)

    return check_product_permissions(user, content_type, event, event_plan_ids)


def check_media_permissions(user, media, content_type):
    media_plan_ids = (settings.CHARGEBEE_COMMUNITY_MONTHLY_PLAN_IDS +
                      settings.CHARGEBEE_COMMUNITY_YEARLY_PLAN_IDS)

    return check_product_permissions(user, content_type, media, media_plan_ids)


def check_discourse_content_permissions(user, thread_id):
    """
    Check if user has permissions to access content associated with a discussion thread.

    :param user: User instance
    :param thread_id: UUID of the discussion thread
    :return: tuple (has_permission: bool, content_data: dict, should_enroll: bool)
    """
    from api.helpers import filter_content_by_discourse_id
    from apps.user_content.models import UserContent

    if not user.is_authenticated:
        return False, None, False

    # Get content associated with this thread
    content = filter_content_by_discourse_id(thread_id)

    if not content:
        # No content associated with this thread - allow access
        return True, content, False

    # Check permissions for each content type
    user_has_permission = False
    should_enroll = False

    slug = content.get('slug')
    content_type = content.get('contentType')

    # Check if user is already registered in this content
    user_content_exists = UserContent.objects.filter(
        user=user, content_type=content_type, slug=slug).exists()

    if user_content_exists:
        # User is already registered
        user_has_permission = True
        should_enroll = True
    elif content_type == 'webinars':
        # For webinars, just add them as participants
        user_has_permission = True
        should_enroll = True
    elif content_type in ['courses', 'events', 'clubs', 'audio-journeys']:
        non_free_plan_ids = (
            settings.CHARGEBEE_THRIVE_MONTHLY_PLAN_IDS +
            settings.CHARGEBEE_THRIVE_YEARLY_PLAN_IDS +
            settings.CHARGEBEE_COMMUNITY_MONTHLY_PLAN_IDS +
            settings.CHARGEBEE_COMMUNITY_YEARLY_PLAN_IDS
        )

        if user.plan_id in non_free_plan_ids:
            # Check if user has non-free plan
            user_has_permission = True
            should_enroll = True
        else:
            # Check if they purchased the content
            has_purchased = UserContent.objects.filter(
                user=user, content_type=content_type, slug=slug, on_demand=True).exists()

            if has_purchased:
                user_has_permission = True
                should_enroll = True
            elif content.get('price', 0) == 0:
                # Check if content is free
                user_has_permission = True
                should_enroll = True

    return user_has_permission, content, should_enroll


def auto_enroll_user_in_discussion(user, thread_id):
    """
    Automatically enroll user in discussion thread if they have content permissions.

    :param user: User instance
    :param thread_id: UUID of the discussion thread
    :return: bool indicating if user was enrolled
    """
    from apps.discourse.models import ChatThread, ThreadParticipant
    from utils.api.enrolment import enrol_user

    try:
        # Get the discussion thread
        thread = ChatThread.objects.get(id=thread_id, discourse_type='discussions')

        if ThreadParticipant.objects.filter(thread=thread, user=user).exists():
            return True

        has_permission, content, should_enroll = \
            check_discourse_content_permissions(user, thread_id)

        if not has_permission or not should_enroll:
            return False

        # Add user as participant
        ThreadParticipant.objects.get_or_create(
            thread=thread,
            user=user
        )

        slug = content.get('slug')
        content_type = content.get('contentType')

        # Check if user needs to be enrolled in this content
        from apps.user_content.models import UserContent
        user_content_exists = UserContent.objects.filter(
            user=user, content_type=content_type, slug=slug).exists()

        if not user_content_exists:
            try:
                enrol_user(content, content_type, user, subscribe=False)
                print(f"auto-enroll: Enrolled user {user.id} in {content_type}/{slug}")
            except Exception as e:
                print(f"auto-enroll: Error enrolling user {user.id} in "
                      f"{content_type}/{slug}: {e}")

        print(f"auto-enroll: Added user {user.id} to discussion thread {thread_id}")
        return True
    except ChatThread.DoesNotExist:
        print(f"auto-enroll: Discussion thread {thread_id} not found")
        return False
    except Exception as e:
        print(f"auto-enroll: Error enrolling user {user.id} in discussion {thread_id}: {e}")
        return False
