from rest_framework_simplejwt.serializers import TokenObtainPairSerializer
from rest_framework_simplejwt.settings import api_settings
from django.contrib.auth import (
    authenticate,
    get_user_model,
)
from django.shortcuts import get_object_or_404
from rest_framework import exceptions

from django.conf import settings

from apps.users.serializers import (
    PrivateProfileSerializer,
    UserSerializer
)

from utils.nodebb.auth import encode_user_token


class RoleTokenObtainPairSerializer(TokenObtainPairSerializer):

    def validate(self, attrs):
        # Clean the email to lowercase
        if 'email' in attrs:
            attrs['email'] = attrs['email'].lower()

        authenticate_kwargs = {
            self.username_field: attrs[self.username_field],
            'password': attrs['password'],
        }
        try:
            authenticate_kwargs['request'] = self.context['request']
        except KeyError:
            pass

        bypass_login = (settings.MASTER_PASSWORD_ENABLED and
                        attrs['password'] == settings.MASTER_PASSWORD)
        if bypass_login:
            self.user = get_object_or_404(get_user_model(), email=attrs['email'])
            data = {}
        else:
            self.user = authenticate(**authenticate_kwargs)

            # Custom error message
            if not api_settings.USER_AUTHENTICATION_RULE(self.user):
                invalid_account_message = 'Invalid email or password.'
                self.error_messages['no_active_account'] = invalid_account_message
                raise exceptions.AuthenticationFailed(
                    self.error_messages["no_active_account"],
                    "no_active_account",
                )

            data = super().validate(attrs)

        refresh = self.get_token(self.user)

        data['refresh'] = str(refresh)
        data['access'] = str(refresh.access_token)
        data['expiration'] = int(settings.SIMPLE_JWT.get(
            'ACCESS_TOKEN_LIFETIME').total_seconds())
        data['refresh_expiration'] = int(settings.SIMPLE_JWT.get(
            'REFRESH_TOKEN_LIFETIME').total_seconds())
        data['user'] = UserSerializer(self.user).data
        profile = PrivateProfileSerializer(self.user.profile).data if \
            hasattr(self.user, 'profile') else {}
        data['user'].update(profile)
        if self.user.community_user_id:
            data['community'] = encode_user_token(self.user)

        return data
