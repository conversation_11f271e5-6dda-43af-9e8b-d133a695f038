import uuid

from django.contrib.auth.base_user import AbstractBaseUser
from django.contrib.auth import get_user_model
from django.contrib.auth.models import PermissionsMixin
from django.db import models
from django.contrib.postgres.fields import A<PERSON>y<PERSON>ield
from django.db.models.signals import post_save
from django.dispatch import receiver

from config import settings

from .constants import (
    NEWSLETTER_MAP,
    get_default_newsletter_subscription,
    get_default_guided_tour_steps,
    get_default_profile_privacy,
    get_default_profile_details,
    get_default_profile_media_preferences,
    get_default_profile_notification_preferences,
    get_default_social_networks,
)
from .managers import UserManager
from utils.hubspot.users import (
    update_activation_token,
    update_password_reset_token,
)

from utils.hubspot.users import (
    subscribe_user,
    unsubscribe_user,
)

from config.storage import UsersPublicMediaStorage



SYNCED_USER_ATTRIBUTES = ['first_name', 'last_name']


def generate_token():
    return uuid.uuid4().hex


class User(AbstractBaseUser, PermissionsMixin):
    id = models.UUIDField('ID',
        max_length=36, primary_key=True, default=uuid.uuid4, editable=False)
    email = models.EmailField(unique=True, max_length=128)
    first_name = models.CharField(max_length=32, default='/')
    last_name = models.CharField(max_length=32, default='/')
    created = models.DateTimeField(auto_now_add=True)
    is_staff = models.BooleanField(default=False)
    is_admin = models.BooleanField(default=False)
    is_teacher = models.BooleanField(default=False)

    # Chargebee
    chargebee_id = models.CharField(max_length=128, blank=True, null=True)
    subscription_id = models.CharField(max_length=128, blank=True, null=True)
    plan_id = models.CharField(
        max_length=128, default=settings.CHARGEBEE_FREE_PLAN_ID)

    # HubSpot
    hubspot_id = models.CharField(max_length=128, blank=True, null=True)
    subscribed_to_newsletter = models.BooleanField(default=False)

    # NodeBB
    community_user_id = models.CharField(max_length=128, blank=True, null=True)
    community_user_username = models.CharField(max_length=128, blank=True, null=True)
    community_groups = ArrayField(
        models.CharField(max_length=256), default=list)

    is_active = models.BooleanField(default=True)
    is_verified = models.BooleanField(default=False)
    activation_token = models.CharField(max_length=128, blank=True, null=True)
    password_reset_token = models.CharField(
        max_length=128, blank=True, null=True)

    objects = UserManager()
    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = []


    class Meta:
        verbose_name = 'user'
        verbose_name_plural = 'users'


    @property
    def full_name(self):
        full_name = f'{self.first_name} {self.last_name}'
        return full_name.strip()

    def name_is_set(self):
        return self.first_name != '/' and self.last_name != '/'

    def __str__(self):
        return self.full_name

    def regenerate_activation_token(self):
        self.activation_token = generate_token()
        self.save()

    def regenerate_password_reset_token(self):
        self.password_reset_token = generate_token()
        self.save()

    def trigger_account_activation_flow(self, prop="account_activation_url"):
        """
        Regenerates the activation token and updates it triggering the user
        account activation workflow
        """
        self.regenerate_activation_token()
        update_activation_token(self, prop)

    def trigger_password_reset_flow(self):
        """
        Regenerates the password reset token and updates it triggering the user
        password reset workflow
        """
        self.regenerate_password_reset_token()
        update_password_reset_token(self)

    def follow(self, user_to_follow):
        """Follow another user by adding their profile to this user's following
         list"""
        return self.profile.follow(user_to_follow)

    def unfollow(self, user_to_unfollow):
        """Unfollow another user by removing their profile from this user's
         following list"""
        return self.profile.unfollow(user_to_unfollow)

class PlanOnHold(models.Model):
    created = models.DateTimeField(auto_now_add=True)
    email = models.CharField(max_length=256)
    chargebee_id = models.CharField(max_length=128, default='None')
    plan_id = models.CharField(max_length=128)
    subscription_id = models.CharField(max_length=128, blank=True, null=True)
    on_hold = models.BooleanField(default=True)
    event = models.JSONField()

    class Meta:
        verbose_name = 'Plan on hold'
        verbose_name_plural = 'Plans on hold'

class GiftPurchase(models.Model):
    created = models.DateTimeField(auto_now_add=True)
    buyer = models.ForeignKey(
        get_user_model(), related_name='gifts_purchased',
        on_delete=models.CASCADE)
    recipient = models.ForeignKey(
        get_user_model(), related_name='gifts_received',
        on_delete=models.CASCADE, blank=True, null=True)
    coupon_code = models.CharField(max_length=128)
    redeemed = models.BooleanField(default=False)


class Profile(models.Model):
    user = models.OneToOneField(
        get_user_model(), related_name='profile', on_delete=models.CASCADE)
    created = models.DateTimeField(auto_now_add=True)
    updated = models.DateTimeField(auto_now=True)
    onboarding_completed = models.BooleanField(default=False)

    picture = models.FileField(
        storage=UsersPublicMediaStorage(), null=True, blank=True)
    picture_tiny = models.FileField(
        storage=UsersPublicMediaStorage(), null=True, blank=True)
    cover = models.FileField(
        storage=UsersPublicMediaStorage(), null=True, blank=True)

    # attribute can have different levels of privacy rules:
    # public, members, friends and private
    privacy_rules = models.JSONField(default=get_default_profile_privacy)
    private_details = models.JSONField(default=get_default_profile_details)
    member_details = models.JSONField(default=get_default_profile_details)
    friends_details = models.JSONField(default=get_default_profile_details)
    public_details = models.JSONField(default=get_default_profile_details)

    interests = models.JSONField(default=dict)
    newsletters = models.JSONField(default=get_default_newsletter_subscription)
    social_networks = models.JSONField(default=get_default_social_networks)
    media_preferences = models.JSONField(
        default=get_default_profile_media_preferences)
    user_notification_preferences = models.JSONField(
        default=get_default_profile_notification_preferences)
    interest_notification_preferences = models.JSONField(
        default=get_default_profile_notification_preferences)
    interest_notification_preferences = models.JSONField(
        default=get_default_profile_notification_preferences)
    guided_tour_steps = models.JSONField(
        default=get_default_guided_tour_steps)


    following = models.ManyToManyField(
        'self',
        through='Followers',
        symmetrical=False,
        related_name='followers')

    @classmethod
    def get_cache_key(cls, suffix=None):
        cache_key = 'user_profiles'
        return f"{cache_key}_{suffix}" if suffix else cache_key

    @property
    def full_name(self):
        return self.user.full_name

    def __str__(self):
        return f"{self.full_name}'s profile"

    def map_details(self, details=None):
        if not details:
            self.private_details.pop('courses', False)
            self.private_details.pop('clubs', False)
            self.private_details.pop('webinars', False)
            self.private_details.pop('audio-journeys', False)
            details = self.private_details
        else:
            details.pop('courses', False)
            details.pop('clubs', False)
            details.pop('webinars', False)
            details.pop('audio-journeys', False)
            self.private_details.update(details)

        details_map = {
            'private': {},
            'members': {},
            'friends': {},
            'public': {}
        }

        for attr, rule in self.privacy_rules.items():
            if attr in details.keys():
                details_map[rule][attr] = details[attr]
            else:
                details_map[rule][attr] = self.private_details.get(attr, '')

        details_map['public']['first_name'] = details.get(
            'first_name', self.user.first_name)
        details_map['public']['last_name'] = details.get(
            'last_name', self.user.last_name)

        if any(details.get(p, False) for p in ['first_name', 'last_name']):
            self.user.first_name = details_map['public']['first_name']
            self.user.last_name = details_map['public']['last_name']
            self.user.save()

        details_map['public']['member_since'] = details.get(
            'member_since', str(self.user.created.year))

        user_content = self.user.content.all()
        details_map['public']['clubs_count'] = user_content.filter(
            content_type='clubs').exclude(enrolment__isnull=True).count()
        details_map['public']['courses_count'] = user_content.filter(
            content_type='courses').exclude(enrolment__isnull=True).count()
        details_map['public']['connections_count'] = self.get_total_connections_count()
        # TODO: add discussions count

        # details_map['members'].update(details_map['public'])
        # details_map['friends'].update(details_map['members'])
        details_map['private'].update(details_map['public'])

        self.private_details = details_map['private']
        # self.member_details = details_map['members']
        # self.friends_details = details_map['friends']
        self.public_details = details_map['public']
        self.save()

    def update_newsletters(self, update_subscriptions):
        for newsletter_key, to_subscribe in update_subscriptions.items():
            if self.newsletters.get(newsletter_key) != to_subscribe:
                newsletter = NEWSLETTER_MAP.get(newsletter_key)
                if to_subscribe:
                    subscribe_user(self.user, newsletter)
                else:
                    unsubscribe_user(self.user, newsletter)

        self.newsletters = update_subscriptions
        self.save()

    def is_following(self, user):
        """Check if this profile is following a specific user"""
        return self.following_relationships.filter(followed=user.profile).exists()

    def is_followed_by(self, user):
        """Check if this profile is followed by a specific user"""
        return self.follower_relationships.filter(
            follower=user.profile).exists()

    def get_mutual_follows(self):
        """Get users that this profile follows who also follow back"""
        # Get users this profile follows
        following_ids = self.following.values_list('id', flat=True)
        # Find users from that list who also follow this profile's user
        return get_user_model().objects.filter(
            profile__following=self.user,
            id__in=following_ids
        )

    def follow(self, user_to_follow):
        """Follow another user's profile"""
        self.following.add(user_to_follow.profile)
        self.map_details()
        user_to_follow.profile.map_details()

    def unfollow(self, user_to_unfollow):
        """Unfollow another user's profile"""
        self.following.remove(user_to_unfollow.profile)
        self.map_details()
        user_to_unfollow.profile.map_details()

    def get_total_connections(self):
        """
        Get a queryset of all profiles that are either following this profile
        or are followed by this profile (or both)

        Returns a queryset of Profile objects that can be used for pagination
        and further filtering.
        """
        # Get profiles this user is following
        following_profiles = self.following.all()

        # Get profiles that follow this user
        follower_profiles = self.followers.all()

        # Combine both querysets
        all_connections = Profile.objects.filter(
            models.Q(id__in=following_profiles.values_list('id', flat=True)) |
            models.Q(id__in=follower_profiles.values_list('id', flat=True))
        ).distinct()

        return all_connections

    def get_total_connections_count(self):
        """
        Get the count of all unique connections (following + followers)
        """
        return self.get_total_connections().count()


class Followers(models.Model):
    """
    Explicit through model for more metadata
    """
    follower = models.ForeignKey(
        'Profile', related_name='following_relationships', on_delete=models.CASCADE)
    followed = models.ForeignKey(
        'Profile', related_name='follower_relationships', on_delete=models.CASCADE)
    created = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ('follower', 'followed')


profiles_cache_key = Profile.get_cache_key()


@receiver(post_save, sender=User)
def user_profile_creation_handler(sender, instance, created, **kwargs):
    if created:
        profile = Profile.objects.create(user=instance)
        profile.map_details()

        # Create default chat thread with founder user
        from apps.discourse.utils import create_default_chat_thread
        create_default_chat_thread(instance)
