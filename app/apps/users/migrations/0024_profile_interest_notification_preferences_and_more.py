# Generated by Django 5.1.4 on 2025-06-05 11:44

import apps.users.constants
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0023_merge_20250529_1309'),
    ]

    operations = [
        migrations.AddField(
            model_name='profile',
            name='interest_notification_preferences',
            field=models.JSONField(default=apps.users.constants.get_default_profile_notification_preferences),
        ),
        migrations.AddField(
            model_name='profile',
            name='media_preferences',
            field=models.JSONField(default=apps.users.constants.get_default_profile_media_preferences),
        ),
        migrations.AddField(
            model_name='profile',
            name='user_notification_preferences',
            field=models.JSONField(default=apps.users.constants.get_default_profile_notification_preferences),
        ),
    ]
