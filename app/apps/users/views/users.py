from rest_framework import serializers
from rest_framework import status
from rest_framework.decorators import action
from rest_framework.generics import UpdateAPIView
from rest_framework.permissions import (
    AllowAny,
    IsAuthenticated,
)
from rest_framework.serializers import ValidationError
from rest_framework.response import Response

from django.core.exceptions import ObjectDoesNotExist
from django.http import HttpResponse
from django.shortcuts import get_object_or_404

from config import settings
from config.exceptions import InvalidRequest

from apps.authentication.permissions import (
    IsChargebeeRequest,
    IsStaff,
    IsStaffOrOwnAccount,
    ReadOnly,
)
from apps.user_content.serializers import CurrentUserSerializer
from apps.users.models import User
from apps.users.permissions import MixedPermissionModelViewSet
from apps.users.serializers import (
    ChangePasswordSerializer,
    UserSerializer,
    ResetPasswordSerializer,
)

from utils.chargebee.subscriptions import event_dispatcher
from utils.hubspot.users import (
    subscribe_email,
    update_user_details as hubspot_update_user_details,
    update_account_verified as hubspot_update_account_verified,
)
from utils.nodebb.auth import (
    update_user_profile_details as nodebb_update_user_details,
    update_account_verified as nodebb_update_account_verified,
)



class UserViewSet(MixedPermissionModelViewSet):
    queryset = User.objects.select_related('profile').filter(is_active=True)
    serializer_class = UserSerializer
    permission_classes = (ReadOnly,)

    def get_permissions(self):
        is_annonymous = ['create',
                         'newsletter_subscription',
                         'request_password_reset']
        is_authenticated = ['verify',
                            'activate']
        is_staff_or_own_account = ['retrieve',
                                   'update',
                                   'partial_update',
                                   'destroy']
        is_chargebee_request = ['set_user_plan']
        is_staff = ['list',
                    'retrieve',
                    'update',
                    'partial_update',
                    'destroy',
                    'set_user_plan']
        if self.action in is_annonymous:
            return [AllowAny()]
        if self.action in is_authenticated:
            return [IsAuthenticated()]
        if self.action in is_staff_or_own_account:
            return [IsStaffOrOwnAccount()]
        if self.action in is_chargebee_request:
            return [IsChargebeeRequest()]
        if self.action in is_staff:
            return [IsStaff()]
        return [permission() for permission in self.permission_classes]

    def create(self, request, *args, **kwargs):
        try:
            response = super().create(request, *args, **kwargs)

            return response
        except Exception as e:
            raise ValidationError({'detail': str(e)})

    def retrieve(self, request, *args, **kwargs):
        if kwargs.get('pk') == 'current':
            return Response(CurrentUserSerializer(request.user).data)
        return super().retrieve(request, args, kwargs)

    def partial_update(self, request, *args, **kwargs):
        response = super().partial_update(request, args, kwargs)

        return response

    def destroy(self, request, *args, **kwargs):
        if kwargs.get('pk') == 'current':
            user = request.user

            # response = super().destroy(request, args, kwargs)
            # if status.is_success(response.status_code):
                # pass
            # return response

            if user.is_active:
                try:
                    hubspot_update_user_details(
                        user, {'member_status': 'Inactive'})
                    user.is_active = False
                    user.first_name = 'Deactivated'
                    user.last_name = 'User'
                    # TODO: remove profile picture
                    # TODO: set default profile picture
                    user.save()
                    # nodebb_update_user_details(user)

                    return Response()
                except Exception as e:
                    raise ValidationError({'detail': str(e)})
        raise InvalidRequest(detail='Invalid request.')

    @action(detail=False, methods=['POST'])
    def set_user_plan(self, request):
        event = request.data.get('event_type')

        if event in event_dispatcher.keys():
            event_dispatcher[event](request.data.get('content'))
        else:
            message = f"Chargebee webhook event '{event}' not implemented."
            if settings.SENTRY_ENABLED:
                from sentry_sdk import capture_message
                capture_message(message)
            print(message)

        return HttpResponse()

    @action(detail=False, methods=['GET'])
    def verify(self, request):
        """Request to regenerate activation token and send via email."""
        if not request.user.is_verified:
            request.user.trigger_account_activation_flow()
            response = {
                'status': 'success',
                'code': status.HTTP_200_OK,
                'message': 'Check your inbox for the activation link.',
                'data': {'detail': 'Check your inbox for the activation link.'}
            }
            return Response(response)
        raise ValidationError({'detail': 'This user is already verified.'})

    @action(detail=False, methods=['GET'])
    def activate(self, request):
        """Activate user account based on activation_token."""
        token = request.query_params.get('token', False)
        user = request.user
        if token and not user.is_verified and user.activation_token == token:
            user.is_verified = True
            user.save()
            hubspot_update_account_verified(user)
            # nodebb_update_account_verified(user)
            return Response(self.get_serializer(user).data)
        raise ValidationError(
            {'detail': 'Your token is invalid. Please request a new verification.'})

    @action(detail=False, methods=['POST'])
    def newsletter_subscription(self, request):
        email = request.data.get('email', False)
        subscription_type = "Newsletter"
        if email:
            try:
                if request.data.get('interests', False):
                    subscription_type = "Categories"
                    subscribe_email(email, settings.HUBSPOT_INTERESTS_LIST_ID)
                    for interest in request.data.get('interests', {}).values():
                        subscribe_email(email, interest.get('hubspot_list_id'))
                else:
                    subscribe_email(email)
                    if (request.user.is_authenticated and
                            request.user.email == email):
                        request.user.subscribed_to_newsletter = True
                        request.user.save()
                return Response()
            except Exception as e:
                error_message = f"{subscription_type} subscription: {email}: {e}"
                print(error_message)
                if settings.SENTRY_ENABLED:
                    from sentry_sdk import capture_message
                    capture_message(error_message)
                raise ValidationError({'detail': str(e)})
        raise ValidationError({'detail': 'Email is required.'})

    @action(detail=False, methods=['GET'])
    def request_password_reset(self, request):
        """Request to reset password and send via email."""
        try:
            if not request.query_params.get('email', False):
                raise InvalidRequest(detail='Email is mandatory.')
            user = get_object_or_404(
                User, email=request.query_params.get('email').lower())
            user.trigger_password_reset_flow()

            response = {
                'status': 'success',
                'code': status.HTTP_200_OK,
                'message': 'Check your inbox for the password reset link.',
                'data': {'detail': 'Check your inbox for the password reset link.'}
            }
            return Response(response)
        except ObjectDoesNotExist:
            raise InvalidRequest(detail='Invalid request, please try again.')


class ChangePasswordView(UpdateAPIView):
    """
    An endpoint for changing password.
    """
    serializer_class = ChangePasswordSerializer
    model = User
    permission_classes = (IsAuthenticated,)

    def get_object(self, queryset=None):
        return self.request.user

    def update(self, request, *args, **kwargs):
        self.object = self.get_object()
        serializer = self.get_serializer(data=request.data)

        if serializer.is_valid():
            # Check old password
            if not self.object.check_password(serializer.data.get("old_password")):
                raise ValidationError({'detail': 'Old password is incorrect.'})
            # set_password also hashes the password that the user will get
            self.object.set_password(serializer.data.get("new_password"))
            self.object.save()
            response = {
                'status': 'success',
                'code': status.HTTP_200_OK,
                'message': 'Password updated successfully',
                'data': {'detail': 'Password updated successfully'}
            }

            return Response(response)

        raise ValidationError({'detail': str(serializer.errors)})


class ResetPasswordView(UpdateAPIView):
    """
    An endpoint for changing password.
    """
    serializer_class = ResetPasswordSerializer
    model = User
    permission_classes = (AllowAny,)

    def get_object(self, queryset=None):
        try:
            return User.objects.get(
                password_reset_token=self.request.data.get('token'))
        except  ObjectDoesNotExist:
            raise InvalidRequest(detail='Invalid token, please try again.')

    def update(self, request, *args, **kwargs):
        self.object = self.get_object()
        serializer = self.get_serializer(data=request.data)

        if serializer.is_valid():
            # set_password also hashes the password that the user will get
            self.object.set_password(serializer.data.get("new_password"))
            self.object.save()
            response = {
                'status': 'success',
                'code': status.HTTP_200_OK,
                'message': 'Password updated successfully',
                'data': {'detail': 'Password updated successfully'}
            }

            return Response(response)

        raise ValidationError({'detail': str(serializer.errors)})
