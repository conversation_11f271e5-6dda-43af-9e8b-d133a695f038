from utils.hubspot.client import (
    GLOBAL_USERS,
    FREE_USERS,
)


NEWSLETTER_MAP = {
    'general': GLOBAL_USERS,
    'monthly': GLOBAL_USERS,  # TODO: check what's monthly newsletter
    'membership': FREE_USERS,  # TODO: check membership newsletter
}

DEFAULT_NEWSLETTER_SUBSCRIPTION = dict(
    general=True,
    monthly=True,
    membership=True,
)

DEFAULT_PROFILE_PRIVACY = {
    'first_name': 'public',
    'last_name': 'public',
    'nickname': 'public',
    'birthdate': 'public',
    'gender': 'public',
    'location': 'public',
    'country': 'public',
    'mobile_phone': 'public',
    'social_media': 'public',
    'biography': 'public',
    'profile_photo_tiny': 'public',
    'profile_photo': 'public',
    'cover_photo': 'public',
    'interests': 'public',
    'courses': 'private',
    'clubs': 'private',
    'webinars': 'private',
    'audio-journeys': 'private',
}

DEFAULT_PROFILE_MEDIA_PREFERENCES = {
  'videos': False,
  'group-programs': False,
  'articles': False,
  'podcasts': False,
  'e-learning': False,
}

DEFAULT_PROFILE_NOTIFICATION_PREFERENCES = {
    'browser': {
        'new-connection': True,
        'new-chat': True,
        'comment-replies': True,
        'discussions-joined': True,
        'reminders': True,
    },
    'email': {
        'new-connection': True,
        'new-chat': True,
        'comment-replies': True,
        'discussions-joined': True,
        'reminders': True,
    }
}

DEFAULT_GUIDED_TOUR_STEPS = [
    {
        'id': 'dashboard',
        'page': 'dashboard',
        'element': '#target-element',
        'title': 'Advaya Dashboard',
        'description': 'This is where you manage all your courses, clubs and everything that you do on advaya.',
        'position': 'bottom',
        'completed': False,
    },
    {
        'id': 'chatWindow',
        'page': 'dashboard',
        'element': '#chat-window',
        'title': 'Chat',
        'description': 'Missing copy',
        'position': 'left',
        'actionBefore': 'toggleChat',
        'actionAfter': 'toggleChat',
        'completed': False,
    },
    {
        'id': 'profile',
        'page': 'profile',
        'element': '#profile-header',
        'title': 'Profile',
        'description': 'Missing copy',
        'position': 'bottom',
        'completed': False,
    }
]

DEFAULT_PROFILE_DETAILS = dict({k: '' for k in DEFAULT_PROFILE_PRIVACY.keys()})

DEFAULT_SOCIAL_NETWORKS = dict(
    facebook='',
    instagram='',
    youtube='',
    website='',
)


def get_default_newsletter_subscription():
    return DEFAULT_NEWSLETTER_SUBSCRIPTION


def get_default_profile_privacy():
    return DEFAULT_PROFILE_PRIVACY


def get_default_profile_details():
    return DEFAULT_PROFILE_DETAILS


def get_default_social_networks():
    return DEFAULT_SOCIAL_NETWORKS


def get_default_profile_media_preferences():
    return DEFAULT_PROFILE_MEDIA_PREFERENCES


def get_default_profile_notification_preferences():
    return DEFAULT_PROFILE_NOTIFICATION_PREFERENCES


def get_default_guided_tour_steps():
    return DEFAULT_GUIDED_TOUR_STEPS
