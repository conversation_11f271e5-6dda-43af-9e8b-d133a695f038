from rest_framework import serializers

from django.contrib.auth import get_user_model

from apps.user_content.models import (
    UserContent,
    UserEnrolment,
    SessionUserContent,
)
from apps.users.serializers import (
    UserSerializer,
    PrivateProfileSerializer,
)

from utils.cache.helpers import get_external_content_type
from utils.cache.content import (
    get_cached_sessions_data,
    get_cached_product_data,
)


User = get_user_model()


flatten_content = lambda content_list: {
    key: value for dictionary in content_list for
    key, value in dictionary.items()
}


class UserContentSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserContent
        read_only_fields = ('created', 'updated')
        fields = ('id',
                  'content_type',
                  'index_id',
                  'slug',
                  'object_data',
                  'user_data',
                  'is_active',
                  'bookmarked',
                  'created',
                  'updated')

    def to_representation(self, instance):
        return {
            'id': instance.id,
            'contentType': instance.content_type,
            'indexId': instance.index_id,
            'slug': instance.slug,
            'objectData': instance.object_data,
            'userData': instance.user_data,
            'isActive': instance.is_active,
            'isFavourite': instance.bookmarked,
            'created': instance.created,
            'updated': instance.updated
        }


class SimpleUserContentSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserContent
        read_only_fields = ('created', 'updated')
        fields = ('id',
                  'content_type',
                  'index_id',
                  'slug',
                  'is_active',
                  'bookmarked',
                  'created',
                  'updated')

    def to_representation(self, instance):
        return {
            'id': instance.id,
            'contentType': instance.content_type,
            'indexId': instance.index_id,
            'slug': instance.slug,
            'isActive': instance.is_active,
            'isFavourite': instance.bookmarked,
            'created': instance.created,
            'updated': instance.updated
        }


class SessionUserContentSerializer(serializers.ModelSerializer):
    class Meta:
        model = SessionUserContent
        fields = ('slug',)

    def to_representation(self, instance):
        return {
            'id': instance.id,
            'contentType': instance.content_type,
            'slug': instance.slug,
            'isFavourite': instance.bookmarked,
        }


class SlugUserContentSerializer(serializers.ModelSerializer):
    progress = serializers.SerializerMethodField()
    sessions = serializers.SerializerMethodField()
    last_watched = serializers.SerializerMethodField()
    enrolment = serializers.SerializerMethodField()
    product_data = serializers.SerializerMethodField()

    class Meta:
        model = UserContent
        fields = ('slug',
                  'progress',
                  'sessions',
                  'last_watched',
                  'enrolment',
                  'product_data',)

    def get_progress(self, instance):
        enrolment = getattr(instance, 'enrolment', None)
        return enrolment.progress if enrolment else 0

    def get_sessions(self, instance):
        enrolment = getattr(instance, 'enrolment', None)
        # return enrolment.completed_sessions if enrolment else []
        return enrolment.watched_sessions if enrolment else {}

    def get_last_watched(self, instance):
        enrolment = getattr(instance, 'enrolment', None)
        return enrolment.last_watched if enrolment else {}

    def get_enrolment(self, instance):
        enrolment = getattr(instance, 'enrolment', False)
        return bool(enrolment)

    def get_product_data(self, instance):
        product_data = get_cached_product_data(instance)

        return product_data

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        return {
            representation['slug']: {
                'progress': representation.pop('progress'),
                'chapters': representation.pop('sessions'),
                'isRegistered': representation.pop('enrolment'),
                'lastWatched': representation.pop('last_watched'),
                'contentType': instance.content_type,
                'isPurchased': instance.on_demand,
                'isActive': instance.is_active,
                'isFavourite': instance.bookmarked,
                **representation.pop('product_data'),
                **representation,
            }
        }


class SlugSessionUserContentSerializer(serializers.ModelSerializer):
    product_data = serializers.SerializerMethodField()
    content_type = serializers.SerializerMethodField()

    class Meta:
        model = SessionUserContent
        fields = ('slug',
                  'product_data',
                  'content_type',)

    def get_content_type(self, instance):
        return instance.content_type

    def get_product_data(self, instance):
        product_data = get_cached_product_data(instance)

        return product_data

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        return {
            representation['slug']: {
                'isFavourite': instance.bookmarked,
                **representation.pop('product_data'),
                **representation,
                'contentType': representation.pop('content_type'),
            }
        }



class UserContentResultsSerializer(serializers.ModelSerializer):
    """
    Serializer for the user content. This is used in the Favourites list, for
    My Courses, My Practices and My Webinars.
    """
    progress = serializers.SerializerMethodField()
    sessions = serializers.SerializerMethodField()
    last_watched = serializers.SerializerMethodField()
    enrolment = serializers.SerializerMethodField()

    class Meta:
        model = UserContent
        fields = ('slug',
                  'progress',
                  'last_watched',
                  'sessions',
                  'enrolment')

    def get_progress(self, instance):
        enrolment = getattr(instance, 'enrolment', None)
        return enrolment.progress if enrolment else 0

    def get_sessions(self, instance):
        enrolment = getattr(instance, 'enrolment', None)
        # return enrolment.completed_sessions if enrolment else []
        return enrolment.watched_sessions if enrolment else {}

    def get_last_watched(self, instance):
        enrolment = getattr(instance, 'enrolment', None)
        return enrolment.last_watched if enrolment else {}

    def get_enrolment(self, instance):
        enrolment = getattr(instance, 'enrolment', False)
        return bool(enrolment)

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        return {
            'progress': representation.pop('progress'),
            'completedChapters': representation.pop('sessions'),
            'isRegistered': representation.pop('enrolment'),
            'lastWatched': representation.pop('last_watched'),
            'isPurchased': instance.on_demand,
            'isActive': instance.is_active,
            'isFavourite': instance.bookmarked,
            'contentType': instance.content_type,
            **representation,
        }



class SessionUserContentResultsSerializer(serializers.ModelSerializer):
    """
    Serializer for the session user content. This is used in the Favourites
    list and for My Practices.
    """
    class Meta:
        model = SessionUserContent
        fields = ('slug',)

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        return {
            'isFavourite': instance.bookmarked,
            'contentType': instance.content_type,
            **representation,
            # **instance.object_data,
        }


class StaffUserContentSerializer(serializers.ModelSerializer):
    """
    Serializer for the staff user.
    """
    user = UserSerializer(read_only=True)

    class Meta:
        model = UserContent
        read_only_fields = ('created', 'updated')
        fields = ('id',
                  'content_type',
                  'index_id',
                  'slug',
                  'user_data',
                  'user',
                  'bookmarked',
                  'is_active',
                  'created',
                  'updated')

    def to_representation(self, instance):
        return {
            'id': instance.id,
            'contentType': instance.content_type,
            'indexId': instance.index_id,
            'slug': instance.slug,
            'userData': instance.user_data,
            'user': instance.user,
            'isActive': instance.is_active,
            'bookmarked': instance.bookmarked,
            'created': instance.created,
            'updated': instance.updated
        }


class CurrentUserSerializer(serializers.ModelSerializer):
    """
    Serializer for the current user.
    """
    id = serializers.UUIDField(format='hex_verbose')
    favourites = serializers.SerializerMethodField()
    audio_journeys = serializers.SerializerMethodField()
    articles = serializers.SerializerMethodField()
    last_watched = serializers.SerializerMethodField()
    courses = serializers.SerializerMethodField()
    films = serializers.SerializerMethodField()
    webinars = serializers.SerializerMethodField()
    clubs = serializers.SerializerMethodField()
    podcasts = serializers.SerializerMethodField()
    profile = serializers.SerializerMethodField()
    has_billing = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = ('id',
                  'email',
                  'first_name',
                  'last_name'
                  'is_verified',
                  'created',
                  'onboarding_completed',
                  'plan_id',
                  'subscribed_to_newsletter',
                  'favourites',
                  'profile',
                  'has_billing',
                  'is_teacher',)

    def get_favourites(self, instance):
        # TODO: integrate caching
        # slug values of all the favourites
        user_content = UserContent.bookmarked_objects.filter(user=instance).values_list(
            'slug', flat=True).distinct()
        user_sessions_content = SessionUserContent.bookmarked_objects.filter(
            user=instance).values_list('slug', flat=True).distinct()
        return list(user_content) + list(user_sessions_content)

    def get_last_watched(self, instance):
        """
        List with the user's 5 last watched content

        Args:
            instance: User instance

        Returns: list of user's 5 last watched content
        """
        results = []
        enrolments = instance.enrolments.select_related('content').order_by(
            "-updated").filter(
            content__is_active=True,
            content__content_type__in=[
                'courses', 'events', 'clubs', 'audio-journeys', 'webinars'
            ]).exclude(last_watched={})
        if enrolments.exists():
            # TODO: get session_details from cache
            results = [{
                'lastUpdated': enrolment.updated,
                **enrolment.last_watched,
            } for enrolment in enrolments[:3]]
        return results

    def get_articles(self, instance):
        """
        Get the user's articles.
        """
        # TODO: integrate caching
        user_articles = instance.content.select_related("enrolment").filter(
            content_type='articles', is_active=True)
        return {article.slug: SlugUserContentSerializer(article, many=True).data
                for article in user_articles}

    def get_films(self, instance):
        """
        Get the user's films.
        """
        # TODO: integrate caching
        user_films = instance.content.select_related("enrolment").filter(
            content_type='films', is_active=True)
        return {film.slug: SlugUserContentSerializer(film, many=True).data
                for film in user_films}

    def get_podcasts(self, instance):
        """
        Get the user's podcasts.
        """
        # TODO: integrate caching
        podcasts = instance.content.select_related("enrolment").filter(
            content_type='podcasts', is_active=True)
        return {podcast.slug: SlugUserContentSerializer(podcast, many=True).data
                for podcast in podcasts}

    def get_courses(self, instance):
        """
        Get the user's courses.
        """
        # TODO: integrate caching
        user_courses = instance.content.select_related("enrolment").filter(
            content_type='courses', enrolment__isnull=False, is_active=True)

        return {course.slug: get_cached_product_data(course, include_user_content=True)
                   for course in user_courses if course != {}}

    def get_webinars(self, instance):
        """
        Get the user's webinars.
        """
        user_webinars = instance.content.select_related("enrolment").filter(
            content_type='webinars', is_active=True)
        return {webinar.slug: get_cached_product_data(webinar)
                for webinar in user_webinars if webinar != {}}

    def get_clubs(self, instance):
        """
        Get the user's clubs.
        """
        # TODO: integrate caching
        user_content = instance.content.select_related("enrolment").all()
        clubs_content = user_content.filter(
            content_type='clubs', enrolment__isnull=False, is_active=True)
        # event_series = SlugUserContentSerializer(
        #     event_series_content, many=True).data
        club_sessions = SlugSessionUserContentSerializer(
            SessionUserContent.events_objects.filter(
                user=instance,
                slug__in=clubs_content.values_list('slug', flat=True)
            ), many=True).data

        results = {}
        for club in clubs_content:
            results[club.slug] = get_cached_product_data(club, include_user_content=True)
        for club_session in club_sessions:
            results.update(club_session)

        return results

    def get_audio_journeys(self, instance):
        """
        Get the user's audio journeys.
        """
        # TODO: integrate caching
        user_content = instance.content.select_related("enrolment").all()
        audio_journeys_content = user_content.filter(
            content_type='audio-journeys', enrolment__isnull=False, is_active=True)
        # event_series = SlugUserContentSerializer(
        #     event_series_content, many=True).data
        audio_journeys_sessions = SlugSessionUserContentSerializer(
            SessionUserContent.events_objects.filter(
                user=instance,
                slug__in=audio_journeys_content.values_list('slug', flat=True)
            ), many=True).data

        audio_journeys = {}
        for audio_journey in audio_journeys_content:
            audio_journeys[audio_journey.slug] = get_cached_product_data(audio_journey, include_user_content=True)
        for audio_journey_session in audio_journeys_sessions:
            audio_journeys.update(audio_journey_session)

        return audio_journeys

    def get_profile(self, instance):
        return PrivateProfileSerializer(instance.profile).data if \
            hasattr(instance, 'profile') else {}

    def get_has_billing(self, instance):
        return bool(instance.chargebee_id)

    def to_representation(self, instance):
        return {
            'id': str(instance.id),
            'email': instance.email,
            'first_name': instance.first_name,
            'last_name': instance.last_name,
            'emailVerified': instance.is_verified,
            'createdAt': instance.created,
            'plan_id': instance.plan_id,
            'subscribedToNewsletter': instance.subscribed_to_newsletter,
            'lastWatched': self.get_last_watched(instance),
            'articles': self.get_articles(instance),
            'audioJourneys': self.get_audio_journeys(instance),
            'clubs': self.get_clubs(instance),
            'courses': self.get_courses(instance),
            'films': self.get_films(instance),
            'podcasts': self.get_podcasts(instance),
            'webinars': self.get_webinars(instance),
            'favourites': self.get_favourites(instance),
            'hasBilling': self.get_has_billing(instance),
            'isTeacher': instance.is_teacher,
            **self.get_profile(instance),
        }


class EnrolmentSerializer(serializers.ModelSerializer):
    user = UserSerializer(read_only=True)
    content = SimpleUserContentSerializer(read_only=True)

    class Meta:
        model = UserEnrolment
        read_only_fields = ('completed_sessions',
                            'watched_sessions',
                            'is_active',
                            'is_completed',
                            'completed_on',
                            'progress',
                            'content')
        fields = ('id',
                  'user',
                  'content',
                  'is_active',
                  'completed_sessions',
                  'watched_sessions',
                  'is_completed',
                  'completed_on',
                  'progress')

    def to_representation(self, instance):
        return {
            'id': instance.id,
            'user': instance.user,
            'content': instance.content,
            'isActive': instance.is_active,
            'completedChapters': instance.completed_sessions,
            'watchedChapters': instance.watched_sessions,
            'isCompleted': instance.is_completed,
            'completedOn': instance.completed_on,
            'progress': instance.progress
        }
