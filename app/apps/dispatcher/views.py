from rest_framework.decorators import action
from rest_framework.permissions import (
    AllowAny,
    IsAuthenticated,
)
from rest_framework import serializers
from rest_framework.response import Response

from django.contrib.auth import get_user_model
from django.db.models import Q
from django.http import HttpResponse

from config import settings
from config.exceptions import InvalidRequest

from api.entities.models import entities
from apps.authentication.permissions import (
    IsChargebeeRequest,
    IsCMSRequest,
    IsStaff,
    IsStaffOrOwnAccount,
    ReadOnly,
)
from apps.users.permissions import MixedPermissionViewSet

from utils.api.products import generate_gift_purchase
from utils.api.user_content import create_past_participants
from utils.chargebee.users import (
    create_portal_session,
    generate_single_purchase_hosted_page,
    generate_subscription_hosted_page,
    validate_coupon,
    retrieve_coupon_code,
    retrieve_portal_session,
    update_subscription_hosted_page,
)
from utils.chargebee.single_purchases import (
    event_dispatcher as purchases_event_dispatcher,
)
from utils.chargebee.subscriptions import (
    event_dispatcher as subscription_event_dispatcher,
)
from utils.cache.helpers import handle_cache_invalidation
from utils.chargebee.users import handle_abandoned_subscription_cart
from utils.nodebb.auth import (
    register_user as nodebb_register_user,
    add_user_email as nodebb_add_user_email,
    update_account_verified as nodebb_update_account_verified,
)
# from utils.sync.data_collector import collect_user_events, get_default_timestamps
# from utils.sync.data_processor import process_sync_data
from apps.discourse.models import (
    ChatThread,
    ThreadParticipant,
)
from apps.user_content.models import (
    UserContent,
    SessionUserContent,
)


User = get_user_model()


class DispatcherViewSet(MixedPermissionViewSet):
    permission_classes = (ReadOnly,)

    def get_permissions(self):
        is_annonymous = ['get_coupon']
        is_authenticated = ['portal_session',
                            'generate_subscription_checkout',
                            'update_subscription_checkout',
                            'generate_gift_checkout']
        is_chargebee_request = ['set_user_plan',
                                'set_purchase']
        is_cms_request = ['import_past_participants',
                          'create_teacher_user_account',
                          'invalidate_cache',
                          'update_content_slug',
                          # 'collect_sync_data',
                          # 'sync_data',
                          'create_discussion_thread']
        is_staff = ['get_coupon',
                    'portal_session',
                    'generate_subscription_checkout',
                    'update_subscription_checkout']
        if self.action in is_annonymous:
            return [AllowAny()]
        if self.action in is_authenticated:
            return [IsAuthenticated()]
        # if self.action in is_staff_or_own_account:
        #     return [IsStaffOrOwnAccount()]
        if self.action in is_chargebee_request:
            return [IsChargebeeRequest()]
        if self.action in is_cms_request:
            return [IsCMSRequest()]
        if self.action in is_staff:
            return [IsStaff()]
        return [permission() for permission in self.permission_classes]

    @action(detail=False, methods=['POST'])
    def set_user_plan(self, request):
        event = request.data.get('event_type')

        if event in subscription_event_dispatcher.keys():
            subscription_event_dispatcher[event](request.data.get('content'))
        else:
            message = f"Chargebee webhook event '{event}' not implemented."
            if settings.SENTRY_ENABLED:
                from sentry_sdk import capture_message
                capture_message(message)
            print(message)

        return HttpResponse()

    @action(detail=False, methods=['POST'])
    def set_purchase(self, request):
        """Request from Chargebee webhook on single purchase update."""
        event = request.data.get('event_type')

        if event in purchases_event_dispatcher.keys():
            purchases_event_dispatcher[event](request.data.get('content'))
        else:
            message = f"Chargebee webhook event '{event}' not implemented."
            if settings.SENTRY_ENABLED:
                from sentry_sdk import capture_message
                capture_message(message)
            print(message)

        return HttpResponse()

    @action(detail=False, methods=['GET'])
    def get_coupon(self, request):
        code = request.query_params.get('code', False)
        if code:
            coupon = retrieve_coupon_code(code)
            if validate_coupon(coupon):
                return Response(coupon)
        raise serializers.ValidationError({'detail': 'Invalid coupon code.'})

    @action(detail=False, methods=['GET'])
    def portal_session(self, request):
        ps_id = request.query_params.get('portal_session_id', False)
        if ps_id:
            portal_data = retrieve_portal_session(ps_id)
        else:
            portal_data = create_portal_session(request.user)

        return Response(portal_data)

    @action(detail=False, methods=['POST'])
    def generate_subscription_checkout(self, request):
        hosted_page = generate_subscription_hosted_page(
            request.data, request.user)
        # TODO: defer handle_abandoned_subscription_cart
        handle_abandoned_subscription_cart(
            request.user, request.data.get("plan-id"))

        return Response(hosted_page)

    @action(detail=False, methods=['POST'])
    def update_subscription_checkout(self, request):
        hosted_page = update_subscription_hosted_page(
            request.data, request.user)

        return Response(hosted_page)

    @action(detail=False, methods=['POST'])
    def import_past_participants(self, request):
        data = request.data.get('participantsData')
        try:
            create_past_participants(
                data.get('contentType'),
                data.get('slug'),
                data.get('emailList'),
            )
            return Response()
        except Exception as e:
            error_message = f"Import past participants: {e}"
            print(error_message)
            if settings.SENTRY_ENABLED:
                from sentry_sdk import capture_message
                capture_message(error_message)
            raise InvalidRequest(detail=str(e))

    @action(detail=False, methods=['POST'])
    def generate_gift_checkout(self, request):
        hosted_page = generate_gift_purchase(
            request.data.get('chargebeeId'), request.user)

        return Response(hosted_page)

    @action(detail=False, methods=['POST'])
    def update_content_slug(self, request):
        data = request.data.get('contentData')
        content_type = data.get('contentType')
        previous_slug = data.get('previousSlug')
        new_slug = data.get('newSlug')
        try:
            if data.get('contentType') in [
                'courses', 'events', 'films', 'articles', 'podcasts', 'webinars']:
                # 'clubs', 'audio-journeys',
                updated_count = UserContent.objects.filter(
                    content_type=content_type, slug=previous_slug).update(
                    slug=new_slug)
                # TODO: invalidate cache when relevant
                print(f"update-content-slug: {content_type}/{previous_slug} -> "
                      f"{content_type}/{new_slug} | ({updated_count} entries)")
                return Response()
            else:
                # TODO: implement course-sessions and event-sessions slug
                print(f"update-content-slug: invalid content type "
                      f"'{content_type}': {previous_slug} -> {new_slug} ")
                raise InvalidRequest(
                    detail=f"Invalid content type: '{content_type}'. "
                           f"Slug update {previous_slug} -> {new_slug} "
                           f"ignored.")
        except Exception as e:
            error_message = f"update-content-slug: {e}"
            print(error_message)
            if settings.SENTRY_ENABLED:
                from sentry_sdk import capture_message
                capture_message(error_message)
            raise InvalidRequest(detail=str(e))

    @action(detail=False, methods=['POST'])
    def create_teacher_user_account(self, request):
        data = request.data.get('teacherData')
        try:
            user, created = User.objects.get_or_create(email=data.get('email'))

            if data.get('picture', False):
                profile_photo = data.get('picture').get('url')
                profile_photo_tiny = data.get('picture', {}).get(
                    'formats', {}).get('thumbnail', {}).get('url')
            else:
                avatar_generator_url = settings.AVATAR_GENERATOR_URL
                user_params = f"{user.first_name}+{user.last_name}".replace(' ', '')
                pic_url = f"{avatar_generator_url}{user_params}"
                profile_photo = f"{pic_url}&background=ffe5e3&size=180"
                profile_photo_tiny = f"{pic_url}&background=ffe5e3&size=48"

            user_profile_details = {
                'nickname': user.email.split('@')[0],
                'bio': data.get('bio'),
                'profile_photo': profile_photo,
                'profile_photo_tiny': profile_photo_tiny,
            }

            if created:
                user.set_password(settings.TEACHER_PASSWORD)
                user.first_name = data.get('first_name')
                user.last_name = data.get('last_name')
                user.is_teacher = True
                user.save()
                user.profile.map_details(user_profile_details)

            if created or not user.community_user_id:
                # triggers account activation workflow
                # hubspot_create_contact(user)
                # user.trigger_account_activation_flow()
                nodebb_register_user(user)
                nodebb_add_user_email(user)
                nodebb_update_account_verified(user)

            if data.get('slug', False):
                teacher = entities.get(data.get('slug'))
                # enrol_user(course, 'courses', user)
                # user.community_groups.append(
                #     course.get('community_group_name'))
                for course in teacher.get('related').get('courses', []):
                    create_past_participants(
                        'courses', course.get('slug'), user.email)

            return Response({
                'user_id': user.id,
                'user_email': user.email,
                'community_user_id': user.community_user_id,
            })
        except Exception as e:
            error_message = f"Create teacher user account: {e}"
            print(error_message)
            if settings.SENTRY_ENABLED:
                from sentry_sdk import capture_message
                capture_message(error_message)
            # raise InvalidRequest(detail=str(e))
            return Response({
                'user_id': user.id,
                'user_email': user.email,
                'community_user_id': user.community_user_id,
            })

    @action(detail=False, methods=['POST'])
    def invalidate_cache(self, request):
        data = request.data.get('cacheData')
        content_type = data.get('contentType')
        slug = data.get('slug', None)
        session_slug = data.get('session', None)

        try:
            handle_cache_invalidation(content_type, slug, session_slug)
            print(f"invalidate-cache: {content_type}/{slug}/{session_slug}")

            return Response()
        except Exception as e:
            error_message = (f"invalidate-cache {content_type}/{slug}: {e}")
            print(error_message)
            if settings.SENTRY_ENABLED:
                from sentry_sdk import capture_message
                capture_message(error_message)
            return Response()

    # Sync methods temporarily commented out due to module import issues
    # TODO: Implement collect_sync_data and sync_data methods

    @action(detail=False, methods=['POST'])
    def create_discussion_thread(self, request):
        """
        Create a discussion thread from CMS with automatic participant enrollment.

        Payload:
        {
            "title": "Discussion Title",
            "content_type": "courses",
            "slug": "course-slug"
        }
        """
        try:
            content_type = request.data.get('contentType')
            slug = request.data.get('contentSlug')
            title = request.data.get('contentTitle')

            if not title or not content_type or not slug:
                raise InvalidRequest(detail="Discussion title, content type, "
                                            "and slug are required")

            discussion_thread = ChatThread.objects.create(
                title=title,
                discourse_type='discussions',
                is_private=True
            )

            user_content_participants = UserContent.objects.filter(
                content_type=content_type, slug=slug).filter(
                Q(on_demand=True) | Q(is_active=True)).values_list(
                'user_id', flat=True).distinct()
                                         # .exclude(
                # Exclude users who only have bookmarked content (not enrolled)
                # models.Q(on_demand=False) & models.Q(is_active=False) & models.Q(bookmarked=True)

            # Find eligible participants from SessionUserContent
            session_content_participants = SessionUserContent.objects.filter(
                content_type=content_type,
                slug=slug
            ).values_list('user_id', flat=True).distinct()

            # Combine and deduplicate participant IDs
            all_participant_ids = set(user_content_participants) | set(session_content_participants)

            # Create ThreadParticipant entries in bulk
            thread_participants = [
                ThreadParticipant(thread=discussion_thread, user_id=user_id)
                for user_id in all_participant_ids
            ]

            if thread_participants:
                ThreadParticipant.objects.bulk_create(
                    thread_participants, ignore_conflicts=True)

            participant_count = len(thread_participants)

            print(f"create-discussion-thread: Created discussion "
                  f"[{str(discussion_thread.id)}:{title}] for "
                  f"{content_type}/{slug} with {participant_count} "
                  f"participants")

            return Response({'discussionId': str(discussion_thread.id)})

        except Exception as e:
            error_message = f"create-discussion-thread: {e}"
            print(error_message)
            if settings.SENTRY_ENABLED:
                from sentry_sdk import capture_message
                capture_message(error_message)
            raise InvalidRequest(detail=str(e))
