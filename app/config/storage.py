from storages.backends.s3 import S3Storage



class StaticStorage(S3Storage):
    location = 'static'
    default_acl = 'public-read'


class PublicMediaStorage(S3Storage):
    location = 'media'
    default_acl = 'public-read'
    file_overwrite = False


class UsersPublicMediaStorage(S3Storage):
    location = 'users'
    default_acl = 'public-read'
    file_overwrite = True


class DiscoursePublicMediaStorage(S3Storage):
    location = 'discourse'
    default_acl = 'public-read'
    file_overwrite = False


class PrivateMediaStorage(S3Storage):
    location = 'private'
    default_acl = 'private'
    file_overwrite = False
    custom_domain = False
