from random import randint
from hashlib import sha256

from django.db.models import Q

from apps.users.serializers import (
    PublicProfileSerializer,
    SimplePublicProfileSerializer,
)
from apps.users.models import Profile

from .helpers import (
    get_cache,
    set_cache,
)

from config import settings



def get_cached_suggested_members_by_interest(interests=[]):
    query = Q()
    if interests:
        for interest in interests:
            query |= Q(interests__has_key=interest)
    # query &= Q(private_details__has_key='location')

    interest_tuple = tuple(sorted(interests))
    raw_key = f"{interest_tuple!r}"
    cache_key = sha256(f"suggested_members_by_interests_{raw_key}".encode(
        'utf-8')).hexdigest()
    results = get_cache(cache_key)

    if not results:
        # filter users that have the similar interests
        results = Profile.objects.filter(query).exclude(
            # Q(public_details__location="") |
            Q(public_details__profile_photo="") |
            Q(public_details__profile_photo__startswith=settings.AVATAR_GENERATOR_URL))
        set_cache(cache_key, results, timeout=settings.MINIMUM_CACHE_TTL)

    # randomize the results and truncate to 10
    s = 0 if len(results) < 10 else randint(0, len(results) - 10)

    suggested_members = SimplePublicProfileSerializer(
        results, many=True).data[s:s + 10]

    return suggested_members


def get_cached_registered_members(content_slugs):
    hashed_query = hash(frozenset(content_slugs))
    cache_key = f'registered_members_{hashed_query}'
    results = get_cache(cache_key)
    if not results:
        results = User.objects.filter(content__slug__in=content_slugs)
        set_cache(cache_key, results, timeout=settings.MINIMUM_CACHE_TTL)

    # randomize the results and truncate to 10
    truncated_results = results.order_by('?')
    results = truncated_results.values(
        'id', 'username', 'first_name', 'last_name', 'location',
        'profile__public_details__picture', 'profile__public_details__location')[:10]
    suggested_members = PublicProfileSerializer(results, many=True).data

    return suggested_members
