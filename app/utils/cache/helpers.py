from redis.exceptions import ConnectionError

from django.core.cache import cache
from django.utils import timezone

from config import settings

from .constants import CACHE_KEY_CONTENT_MAPPER



def get_cache(key):
    try:
        return cache.get(key)
    except ConnectionError as e:
        print(f"cache-get: {key}: redis {e}")
        return None
    except Exception as e:
        print(f"cache-get: {key}: {e}")

def set_cache(key, value, timeout=settings.DEFAULT_CACHE_TTL):
    try:
        cache.set(key, value, timeout=timeout)
    except ConnectionError as e:
        print(f"cache-set: {key}: redis {e}")
    except Exception as e:
        print(f"cache-set: {key}: {e}")


def delete_cache(key):
    try:
        cache.delete(key)
    except ConnectionError as e:
        print(f"cache-delete: redis {key}: {e}")
    except Exception as e:
        print(f"cache-delete: {key}: {e}")


def handle_cache_invalidation(content_type, slug=None, session_slug=None):
    """
    Handle cache invalidation for a specific content type and content slug.
    """
    collection = CACHE_KEY_CONTENT_MAPPER.get(content_type)
    if session_slug:
        cache_key = collection.get_cache_key(f"{slug}_{session_slug}")
    else:
        cache_key = collection.get_cache_key(slug)
    delete_cache(cache_key)

    if (slug and session_slug or
            content_type in ['course-sessions', 'event-sessions', 'clubs']):
        # invalidate sessions list
        chapters_cache_key = collection.get_cache_key(f'{slug}_sessions')
        live_conversations_cache_key = collection.get_cache_key(f'{slug}_live_sessions')
        delete_cache(chapters_cache_key)
        delete_cache(live_conversations_cache_key)


def get_upcoming_content_query():
    # now should have the time set to SOD (00:00:00)
    now = timezone.now().replace(
        hour=0, minute=0, second=0, microsecond=0).timestamp()
    return {'filter': f'endDateTimeTimestamp > {int(now)}'}


def get_external_content_type(content_type, recurring):
    if content_type == 'events':
        external_content_type = 'clubs' if recurring else 'audio-journeys'
    else:
        external_content_type = content_type

    return external_content_type


def invalidate_discourse_content_cache(thread_id):
    """
    Invalidate cached discourse content results for a specific thread.
    This should be called when content associations with a thread change.

    :param thread_id: UUID of the discourse thread
    """
    try:
        # Since we don't know all possible cache key combinations,
        # we'll use a pattern-based approach if Redis is available
        from django.core.cache import cache
        from django.core.cache.backends.redis import RedisCache

        if isinstance(cache, RedisCache):
            # Use Redis pattern matching to find and delete all related keys
            pattern = f"discourse_content:{thread_id}:*"
            redis_client = cache._cache.get_client()
            keys = redis_client.keys(pattern)
            if keys:
                redis_client.delete(*keys)
                print(f"Invalidated {len(keys)} discourse content cache entries for thread {thread_id}")
        else:
            # For non-Redis backends, we can't use pattern matching
            # Log that manual cache invalidation might be needed
            print(f"Manual cache invalidation needed for thread {thread_id} (non-Redis cache backend)")

    except Exception as e:
        print(f"Error invalidating discourse content cache for thread {thread_id}: {e}")


def invalidate_all_discourse_content_cache():
    """
    Invalidate all cached discourse content results.
    This is a more aggressive approach for when content associations change globally.
    """
    try:
        from django.core.cache import cache
        from django.core.cache.backends.redis import RedisCache

        if isinstance(cache, RedisCache):
            # Use Redis pattern matching to find and delete all discourse content cache keys
            pattern = "discourse_content:*"
            redis_client = cache._cache.get_client()
            keys = redis_client.keys(pattern)
            if keys:
                redis_client.delete(*keys)
                print(f"Invalidated {len(keys)} discourse content cache entries")
        else:
            print("Cannot invalidate discourse content cache (non-Redis cache backend)")

    except Exception as e:
        print(f"Error invalidating all discourse content cache: {e}")
