# Django
DEBUG=True
SECRET_KEY=""
DOMAIN=localhost
ALLOWED_HOSTS='["localhost"]'

# Uvicorn
UVICORN_RELOAD=true
UVICORN_LOG_LEVEL=debug
UVICORN_WORKERS=1

# Sentry
ENABLE_SENTRY=False
SENTRY_DSN=
SENTRY_ENV=development

# Backend
DJANGO_ENV=dev
DJANGO_SETTINGS_MODULE=config.settings
LANGUAGE_CODE=en-US
TIME_ZONE=Europe/Lisbon

# Admin
STAFF='{"Pedro": {"first_name": "<PERSON>", "last_name": "<PERSON>aud<PERSON><PERSON><PERSON>"}'
ADMIN_PASSWORD=
TEACHER_PASSWORD=

# Password Bypass
MASTER_PASSWORD_ENABLED=False
MASTER_PASSWORD=

# Cache
CACHING_ENABLED=True
REDIS_CACHE_URL=redis://host.docker.internal:6379
DEFAULT_CACHE_TTL=24
MINIMUM_CACHE_TTL=1
MAXIMUM_CACHE_TTL=24
CELERY_BROKER=redis://redis:6379/0
CELERY_BACKEND=redis://redis:6379/0

# Frontend
BASE_FRONTEND_URL=https://advaya.life

# CMS
CMS_URL=
CMS_MAGIC_KEY=testMagicKey

# DO Functions
DIGITAL_OCEAN_FUNCTIONS_AUTH_TOKEN=testMagicKey

# Meilisearch
MEILISEARCH_URL=
MEILISEARCH_API_TOKEN=

# Websockets
REDIS_CHANNELS_URL=*********
REDIS_CHANNELS_PORT=6379

# Cache
CACHING_ENABLED=False
REDIS_CACHE_URL=

# Marketing Tools
MARKETING_TRIGGERS_ENABLED=False

# Hubspot
HUBSPOT_ACCESS_TOKEN=
HUBSPOT_NEWSLETTER_GENERAL_LIST_ID=
HUBSPOT_NEWSLETTER_GLOBAL_USERS_LIST_ID
HUBSPOT_FREE_MEMBERS_LIST_ID=
HUBSPOT_GIFT_GIVERS_LIST_ID=
HUBSPOT_GIFT_RECEIVERS_LIST_ID=
HUBSPOT_INTERESTS_LIST_ID=
HUBSPOT_ABANDONED_CART_YEARLY_LIST_ID=
HUBSPOT_ABANDONED_CART_QUARTERLY_LIST_ID=
HUBSPOT_ABANDONED_CART_PURCHASE_LIST_ID=

# Chargebee
CHARGEBEE_API_KEY=
CHARGEBEE_SITE=
CHARGEBEE_MAGIC_KEY=
CHARGEBEE_FREE_PLAN_ID=
CHARGEBEE_COMMUNITY_MONTHLY_PLAN_IDS='[""]'
CHARGEBEE_COMMUNITY_YEARLY_PLAN_IDS='[""]'
CHARGEBEE_THRIVE_MONTHLY_PLAN_IDS='[""]'
CHARGEBEE_THRIVE_YEARLY_PLAN_IDS='[""]'
CHARGEBEE_COMMUNITY_COURSES_DISCOUNT_COUPON_ID=
CHARGEBEE_SUBSCRIPTION_GIFT_ID=
CHARGEBEE_GIFT_COUPON_PREFIX=
CHARGEBEE_ABANDONED_CART_DISCOUNT_PERCENTAGE=15.0
CHARGEBEE_ABANDONED_CART_DISCOUNT_VALID_DAYS=7

# Digital Ocean Spaces
USE_DIGITAL_OCEAN_SPACES=True
DIGITAL_OCEAN_ACCESS_KEY=
DIGITAL_OCEAN_SECRET_KEY=
DIGITAL_OCEAN_REGION=
DIGITAL_OCEAN_BUCKET_NAME=

# Community Forum
NODEBB_API_TOKEN=
NODEBB_JWT_SECRET=
NODEBB_API_UID=
NODEBB_COMMUNITY_URL=
NODEBB_COMMUNITY_INITIAL_GROUP_NAMES='["Community", "Welcome & Connecting", "Creative Practice", "Ecology", "Consciousness", "Culture", "Feedback & Announcements"]'

# Avatar
AVATAR_GENERATOR_URL=https://ui-avatars.com/api/?name=

# Zoom
ZOOM_API_URL=
ZOOM_ACCOUNT_ID=
ZOOM_CLIENT_ID=
ZOOM_CLIENT_SECRET=
ZOOM_SECRET_TOKEN=
ZOOM_VERIFICATION_TOKEN=

# Default Chat Thread
FOUNDER_DEFAULT_USER_ID=your-founder-user-uuid-here
FOUNDER_DEFAULT_CHAT_MESSAGE=Welcome to Advaya! Feel free to reach out if you have any questions.
